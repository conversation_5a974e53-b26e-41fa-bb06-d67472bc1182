package com.vedeng.temporal.polling.universal.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.Duration;
import java.util.Map;

/**
 * API配置类 - 极简版
 * 
 * 专为polling组件设计的极简API配置，只保留核心必需参数。
 * 提供一行代码创建配置的能力，大幅简化调用方的使用复杂度。
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (极简版)
 * @since 2025-01-24
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class ApiConfig {
    
    /**
     * API路径 (必需)
     * 例如："/api/v1/order/status"
     */
    private String apiPath;
    
    /**
     * 请求参数 (必需)
     * 将作为HTTP请求的Body参数发送
     */
    private Map<String, Object> parameters;
    
    /**
     * 公司代码 (必需)
     * 用于多租户场景下的API调用
     */
    private String companyCode;
    
    /**
     * 请求超时时间
     * 默认30秒，适合大部分业务场景
     */
    @Builder.Default
    private Duration timeout = Duration.ofSeconds(30);
    
    /**
     * 系统来源标识
     * 默认TEMPORAL，用于API的权限控制和日志记录
     */
    @Builder.Default
    private String systemSource = "TEMPORAL";
    
    /**
     * 极简创建方法 - 推荐使用
     * 
     * 一行代码创建API配置，自动设置合理的默认值。
     * 
     * @param apiPath API路径
     * @param parameters 请求参数
     * @param companyCode 公司代码
     * @return API配置实例
     */
    public static ApiConfig create(String apiPath, Map<String, Object> parameters, String companyCode) {
        return new ApiConfig(apiPath, parameters, companyCode, Duration.ofSeconds(30), "TEMPORAL");
    }
    
    /**
     * 验证配置的有效性
     * 
     * @throws IllegalArgumentException 如果配置无效
     */
    public void validate() {
        if (apiPath == null || apiPath.trim().isEmpty()) {
            throw new IllegalArgumentException("API路径不能为空");
        }
        
        if (companyCode == null || companyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("公司代码不能为空");
        }
        
        if (timeout == null || timeout.isNegative() || timeout.isZero()) {
            throw new IllegalArgumentException("超时时间必须为正数");
        }
    }
}