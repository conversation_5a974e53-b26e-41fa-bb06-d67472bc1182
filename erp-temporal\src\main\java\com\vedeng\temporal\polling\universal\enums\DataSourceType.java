package com.vedeng.temporal.polling.universal.enums;

/**
 * 数据源类型枚举
 * 
 * 定义轮询组件支持的数据源类型，用于区分不同的查询执行方式。
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
public enum DataSourceType {
    
    /**
     * 远程API数据源
     * 通过HTTP API调用获取数据，适用于跨系统、跨网络的数据查询
     */
    REMOTE_API("remote_api", "远程API数据源"),
    
    /**
     * 本地数据库数据源
     * 通过Mapper直接查询本地数据库，适用于本地数据查询
     */
    LOCAL_DATABASE("local_database", "本地数据库数据源");
    
    /**
     * 数据源类型代码
     */
    private final String code;
    
    /**
     * 数据源类型描述
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 数据源类型代码
     * @param description 数据源类型描述
     */
    DataSourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取数据源类型代码
     * 
     * @return 数据源类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取数据源类型描述
     * 
     * @return 数据源类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取数据源类型
     * 
     * @param code 数据源类型代码
     * @return 数据源类型枚举值
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static DataSourceType fromCode(String code) {
        for (DataSourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据源类型代码: " + code);
    }
}