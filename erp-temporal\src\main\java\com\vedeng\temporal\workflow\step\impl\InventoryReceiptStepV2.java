package com.vedeng.temporal.workflow.step.impl;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.workflow.activity.InventoryReceiptActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 入库单步骤 V2 - 新架构版本
 * 
 * 架构优化说明：
 * - 使用新的 InventoryReceiptActivity 替代直接的 CompanyBusinessActivity 调用
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：查询快递信息 → 创建入库单 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * 
 * 业务流程：
 * 1. awaitPurchaseOrderQueryCompletion - 查询采购单号并存储到扩展属性
 * 2. awaitExpressQueryCompletion - 轮询等待快递状态准备就绪 (expressStatus=1)
 * 3. createExpressOnly - 查询快递信息并创建快递
 * 4. awaitStockQueryCompletion - 查询库存记录入参准备
 * 5. createPeerListWithStockData - 基于库存数据创建同行单
 * 6. waitForCompletion - 等待入库单处理完成（可选）
 * 
 * 功能迁移：
 * - 从 InventoryReceiptStep 迁移核心业务逻辑
 * - 保持与 SalesOrderStepV2、InvoiceEntryStepV2 的架构一致性
 * - 优化数据库查询逻辑，使用 Activity 统一处理
 * 
 * 错误处理：
 * - Activity层：技术异常自动重试（网络、超时等）
 * - Step层：业务异常处理（数据校验、业务规则等）
 * - 完整的状态追踪和日志记录
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (新架构版本)
 * @since 2025-01-21
 */
@Slf4j
public class InventoryReceiptStepV2 implements BusinessStep {
    
    private final InventoryReceiptActivity inventoryReceiptActivity;
    
    public InventoryReceiptStepV2(InventoryReceiptActivity inventoryReceiptActivity) {
        this.inventoryReceiptActivity = inventoryReceiptActivity;
    }
    
    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        log.info("开始执行入库单步骤V2，业务ID: {}, 目标公司: {}", 
                request.getBusinessId(), request.getTargetCompanyCode());
        
        String currentCompany = context.getCurrentCompany();
        String nextCompany = context.getNextCompany();
        request.setSourceCompanyCode(currentCompany);
        request.setTargetCompanyCode(context.getNextCompany());
        request.getExtendedProperties().put("isFirst",context.isFirst() ? 1 : 0);
        // 最后一家公司跳过处理
        if (context.isLast()) {
            log.info("执行最后一家公司：{}，无需处理入库单", currentCompany);
            return CompanyBusinessResponse.success("最后一家公司无需处理，跳过执行", request.getBusinessId());
        }
        
        try {
            // 第1步：查询当前公司的采购单号
            log.info("开始查询采购单号，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            awaitPurchaseOrderQueryCompletion(currentCompany, request,"buyOrderNo");
            awaitPurchaseOrderQueryCompletion(nextCompany, request,"nextBuyOrderNo");
            log.info("采购单号查询完成，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());

            // 第2步：根据采购单号查询快递信息并等待快递状态准备就绪
            log.info("开始查询快递信息，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            Map<String, Object> resultData = awaitExpressQueryCompletion(currentCompany, request);
            log.info("快递信息查询完成，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            
            // 第3步：创建快递
            CompanyBusinessResponse expressCreateResult = inventoryReceiptActivity.createExpressOnly(request, resultData);
            if (!expressCreateResult.getSuccess()) {
                log.error("创建快递失败，业务ID: {}, 错误: {}",
                        request.getBusinessId(), expressCreateResult.getMessage());
                return expressCreateResult;
            }
            
            log.info("快递创建成功，业务ID: {}, 快递ID: {}", request.getBusinessId(),
                    expressCreateResult.getGeneratedDocumentId());

            // 第4步：查询库存记录并创建同行单
            // 第4.1步：查询库存记录入参准备
            log.info("开始查询库存记录，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());
            Map<String, Object> stockResultData = awaitStockQueryCompletion(currentCompany, request);
            log.info("库存记录查询完成，公司: {}, 业务ID: {}", currentCompany, request.getBusinessId());

            // 第4.2步：创建同行单
            CompanyBusinessResponse peerListResult = inventoryReceiptActivity.createPeerListWithStockData(request, stockResultData);
            if (!peerListResult.getSuccess()) {
                log.error("创建同行单失败，业务ID: {}, 错误: {}",
                        request.getBusinessId(), peerListResult.getMessage());
                return peerListResult;
            }
            
            String documentId = peerListResult.getGeneratedDocumentId();
            log.info("库存查询和同行单创建成功，业务ID: {}, 同行单ID: {}", request.getBusinessId(), documentId);

            return CompanyBusinessResponse.success("入库单流程执行成功", documentId);
            
        } catch (Exception e) {
            log.error("入库单步骤V2执行异常，业务ID: {}", request.getBusinessId(), e);
            return CompanyBusinessResponse.failure("入库单流程执行失败: " + e.getMessage(), "STEP_EXECUTION_ERROR");
        }
    }
    
    @Override
    public String getStepName() {
        return "入库单步骤V2";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.INVENTORY_RECEIPT;
    }

    @Override
    public String getStepDescription() {
        return "执行入库单完整流程：查询采购单号 → 等待快递状态 → 创建快递 → 查询库存记录 → 创建同行单";
    }
    
    @Override
    public void updateFlowOrderInfo(FlowOrderInfoUpdateRequest updateRequest,
                                   CompanyBusinessRequest request) {
        // 库存入库步骤的状态更新逻辑
        updateRequest.setStorageStatus(2); // 已入库

        log.debug("入库单步骤V2更新汇总信息，入库状态: {}, flowNodeId: {}",
                updateRequest.getStorageStatus(), updateRequest.getFlowNodeId());
    }
    
    // ========== 私有辅助方法 ==========
    

    
    /**
     * 第1步：等待采购单号查询完成
     * 使用通用化枚举设计的轮询机制等待采购单号准备就绪
     */
    private void awaitPurchaseOrderQueryCompletion(String companyCode, CompanyBusinessRequest request,String aliasName) {
        log.info("开始轮询等待采购单号，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 构建查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("BUY_ORDER"));
            queryParameters.put("currentCompany", companyCode);
            
            // 构建统一轮询请求 - 使用数据库轮询
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.LOCAL_DATABASE)
                    .companyCode(companyCode)
                    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                    .queryParameters(queryParameters)
                    .completionCheckConfig("buyOrderNo:isNotBlank")
                    .build();
            
            // 执行统一轮询（使用静态方法保持确定性）
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);
            
            // 轮询成功后，需要手动获取最终数据并设置到扩展属性
            if (result.isSuccess()) {
                // 由于轮询已完成，直接查询一次获取最终数据
                TemporalFlowOrderMapper temporalFlowOrderMapper = ErpSpringBeanUtil.getBean(TemporalFlowOrderMapper.class);
                
                String buyOrderNo = temporalFlowOrderMapper.selectSaleOrderIdByCompanyAndFlowOrder(
                        companyCode, Long.valueOf(businessId), 0);
                
                if (StrUtil.isBlank(buyOrderNo)) {
                    buyOrderNo = temporalFlowOrderMapper.selectBusinessNoByCompanyAndFlowOrder(
                            companyCode, Long.valueOf(businessId));
                }
                
                // 存储到扩展属性
                Map<String, Object> extendedProperties = request.getExtendedProperties();
                if (extendedProperties == null) {
                    extendedProperties = new HashMap<>();
                }
                extendedProperties.put(aliasName, buyOrderNo);
                request.setExtendedProperties(extendedProperties);

                log.info("采购单号轮询完成，公司: {}, 业务ID: {}, 采购单号: {}", 
                        companyCode, businessId, buyOrderNo);
            } else {
                throw BusinessProcessException.fromStep("采购单号轮询未完成", "POLLING_INCOMPLETE", true, getStepName(), companyCode, businessId);
            }

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("采购单号轮询异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            throw BusinessProcessException.fromStep("采购单号轮询系统异常", "POLLING_ERROR", true, getStepName(), companyCode, businessId);
        }
    }

    /**
     * 第2步：等待快递信息查询完成，判断expressStatus是否为1
     * 从 InventoryReceiptStep 迁移的逻辑，用于轮询等待快递状态准备就绪
     *
     * @return
     */
    private Map<String, Object> awaitExpressQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询快递信息，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                throw BusinessProcessException.fromStep("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", false, getStepName(), companyCode, businessId);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                throw BusinessProcessException.fromStep("未找到采购单号", "MISSING_BUY_ORDER_NO", false, getStepName(), companyCode, businessId);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);

            // 构建统一轮询请求
            UniversalPollingRequest statusRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/express/query.do")  // 快递查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 快递查询完成条件：expressStatus为1表示可以创建
                    .completionCheckConfig("expressStatus:1")
                    .build();

            log.info("使用快递查询检查器：查询快递信息，采购单号: {}", buyOrderNo);

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(statusRequest);
            log.info("轮询结果: {}", finalResult);
            // 验证最终状态
            if (!finalResult.isSuccess()) {
                throw BusinessProcessException.fromStep("快递信息查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", true, getStepName(), companyCode, businessId);
            }

            log.info("快递信息查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");
            
            return finalResult.getData();
        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询快递信息异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            throw BusinessProcessException.fromStep("快递信息查询系统异常", "POLLING_ERROR", true, getStepName(), companyCode, businessId);
        }
    }

    /**
     * 第4.1步：等待库存记录查询完成
     * 参考 awaitExpressQueryCompletion 的模式，查询库存记录并返回结果数据
     */
    private Map<String, Object> awaitStockQueryCompletion(String companyCode, CompanyBusinessRequest request) {
        log.info("开始查询库存记录，公司: {}, 业务ID: {}", companyCode, request.getBusinessId());
        String businessId = request.getBusinessId();

        try {
            // 从扩展属性中获取采购单号
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                throw BusinessProcessException.fromStep("扩展属性为空，无法获取采购单号", "MISSING_EXTENDED_PROPERTIES", false, getStepName(), companyCode, businessId);
            }

            String buyOrderNo = (String) extendedProperties.get("buyOrderNo");
            if (buyOrderNo == null) {
                throw BusinessProcessException.fromStep("未找到采购单号", "MISSING_BUY_ORDER_NO", false, getStepName(), companyCode, businessId);
            }

            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("buyOrderNo", buyOrderNo);
            apiParameters.put("isFirst", extendedProperties.get("isFirst"));

            log.info("StepV2调用/api/v1/peerlist/queryStockRecords.do，入参：{}", apiParameters);
            // 构建统一轮询请求
            UniversalPollingRequest stockRequest = UniversalPollingRequest.builder()
                    .businessId(businessId)
                    .businessType(getStepType().getCode())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(companyCode)
                    .apiPath("/api/v1/peerlist/queryStockRecords.do")  // 库存查询API路径
                    .apiParameters(apiParameters)  // 使用自定义API参数
                    // 库存查询完成条件：有数据返回即可
                    .completionCheckConfig("peerStatus:1")
                    .build();

            log.info("使用库存查询检查器：查询库存记录，采购单号: {}, isFirst: {}", buyOrderNo, extendedProperties.get("isFirst"));

            // 执行轮询等待
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(stockRequest);
            log.info("同行单入参库存查询轮询结果: {}", finalResult);

            // 验证最终状态
            if (!finalResult.isSuccess()) {
                throw BusinessProcessException.fromStep("库存记录查询轮询失败: " + finalResult.getMessage(), "POLLING_INCOMPLETE", true, getStepName(), companyCode, businessId);
            }

            log.info("库存记录查询完成，公司: {}, 业务ID: {}, 轮询结果: {}", companyCode, businessId, finalResult.isSuccess() ? "成功" : "失败");

            return finalResult.getData();
        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("查询库存记录异常，公司: {}, 业务ID: {}", companyCode, businessId, e);
            throw BusinessProcessException.fromStep("库存记录查询系统异常", "POLLING_ERROR", true, getStepName(), companyCode, businessId);
        }
    }
}



