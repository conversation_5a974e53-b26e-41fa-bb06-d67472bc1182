package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoQueryRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoResponse;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;

/**
 * 业务流程汇总信息活动接口
 * 负责处理多公司业务流程中的汇总信息记录
 * 
 * 设计特点：
 * - 支持单个和批量更新操作
 * - 提供查询汇总信息功能
 * - 集成 Temporal 重试机制
 * - 异步处理，不影响主业务流程
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-14
 */
@ActivityInterface
public interface FlowOrderInfoActivity {

    /**
     * 更新流程汇总信息
     * 
     * 功能说明：
     * - 根据节点ID更新对应的汇总信息
     * - 支持部分字段更新（null值字段不更新）
     * - 自动处理创建时间和修改时间
     * 
     * 重试机制：
     * - 技术异常：依赖 Temporal Activity 重试（3次，指数退避）
     * - 业务异常：不重试，记录日志
     *
     * @param request 汇总信息更新请求
     * @throws RuntimeException 当更新失败时抛出异常
     */
    @ActivityMethod
    void updateFlowOrderInfo(FlowOrderInfoUpdateRequest request);


    /**
     * 查询流程汇总信息
     * 
     * 查询功能：
     * - 支持按节点ID查询
     * - 支持按业务编号查询
     * - 支持按业务类型查询
     *
     * @param request 查询请求参数
     * @return 汇总信息响应对象
     */
    @ActivityMethod
    FlowOrderInfoResponse queryFlowOrderInfo(FlowOrderInfoQueryRequest request);

    /**
     * 检查汇总信息是否存在
     * 
     * 便捷方法：
     * - 快速检查指定节点的汇总信息是否已存在
     * - 用于避免重复创建
     *
     * @param flowNodeId 流程节点ID
     * @return true-存在，false-不存在
     */
    @ActivityMethod
    boolean existsByFlowNodeId(Long flowNodeId);

}
