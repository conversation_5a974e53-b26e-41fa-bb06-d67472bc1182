package com.vedeng.temporal.workflow.process;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.workflow.activity.*;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.exception.ErrorClassifier;
import com.vedeng.temporal.workflow.step.impl.InvoiceEntryStepV2;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 发票录入流程服务
 * 基于AbstractBusinessProcess重构，使用统一的执行框架
 * <p>
 * 执行特点：
 * - 逆序执行（D → C → B → A）
 * - 复杂的跨公司依赖：当前公司采购单完成 + 下游公司销售发票完成
 * - 包含销售发票开票、发票录入步骤
 * - 使用AbstractBusinessProcess的统一框架
 *
 * <AUTHOR> 4.0 sonnet
 * @version 4.0 (AbstractBusinessProcess重构版)
 * @since 2025-01-11
 */
@Slf4j
public class InvoiceEntryProcess extends AbstractBusinessProcess {

    private final PollingActivity pollingActivity;
    private final InvoiceEntryActivity invoiceEntryActivity;

    public InvoiceEntryProcess(PollingActivity pollingActivity,
                               NotificationActivity notificationActivity,
                               FlowOrderInfoActivity flowOrderInfoActivity,
                               CompanySequenceActivity companySequenceActivity,
                               InvoiceEntryActivity invoiceEntryActivity,
                               ErrorClassifier errorClassifier) {

        // 调用父类构造函数
        super(notificationActivity, flowOrderInfoActivity, companySequenceActivity, pollingActivity, errorClassifier);

        this.pollingActivity = pollingActivity;
        this.invoiceEntryActivity = invoiceEntryActivity;
    }

    @Override
    protected ExecutionMode getExecutionMode() {
        return ExecutionMode.PARALLEL;
    }

    @Override
    protected boolean isReverseOrder() {
        return true; // 不需要逆序
    }

    @Override
    protected List<BusinessStep> getBusinessSteps() {
        // 发票录入流程包含发票录入步骤
        return Collections.singletonList(
                new InvoiceEntryStepV2(invoiceEntryActivity)
        );
    }

    @Override
    protected String getProcessName() {
        return "发票录入并行流程";
    }

    /**
     * 获取流程描述
     *
     * @return 流程描述
     */
    public String getProcessDescription() {
        return "多公司并行执行发票录票，每个公司独立检查开票条件";
    }


    /**
     * 执行发票录入流程
     * 使用AbstractBusinessProcess统一框架
     *
     * @param request         业务请求
     * @param companySequence 公司执行序列
     * @return 执行结果
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        return super.execute(request, companySequence);
    }
}
