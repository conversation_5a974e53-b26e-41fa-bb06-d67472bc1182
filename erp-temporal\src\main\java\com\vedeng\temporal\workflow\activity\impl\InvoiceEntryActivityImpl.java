package com.vedeng.temporal.workflow.activity.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.workflow.activity.InvoiceEntryActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 发票录入 Activity 实现类
 *
 * 架构迁移说明：
 * - 从 InvoiceEntryFlow 迁移核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 保持与原 InvoiceEntryFlow 完全一致的业务逻辑和API调用
 *
 * 业务流程：
 * 1. createInvoiceEntry - 执行完整发票录入流程，包含查询详情、录入、审核
 * 2. queryInvoiceDetail - 查询发票详情，用于流程控制
 * 3. approveInvoice - 审核发票，用于流程控制
 * 4. createInvoiceWithDetails - 基于预查询数据创建发票录入
 *
 * 迁移内容：
 * - execute 方法迁移为 createInvoiceEntry 方法
 * - executeInvoiceEntry、executeSubmitApproval、executeApproval 逻辑集成
 * - extractInvoiceId、extractApproveResult 方法保持不变
 * - 保持原有的API路径和参数结构
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (架构迁移版本，从 InvoiceEntryFlow 迁移)
 * @since 2025-01-21
 */
@Component
@Slf4j
public class InvoiceEntryActivityImpl implements InvoiceEntryActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;



    @Override
    public CompanyBusinessResponse createInvoiceWithDetails(CompanyBusinessRequest request,
                                                            Map<String, Object> invoiceDetailData) {
        try {
            log.info("开始基于预查询数据创建发票录入，业务ID: {}", request.getBusinessId());

            // 调用现有的创建发票方法
            CompanyBusinessResponse invoiceCreateResult = createInvoice(request, invoiceDetailData);
            if (!Boolean.TRUE.equals(invoiceCreateResult.getSuccess())) {
                return invoiceCreateResult;
            }

            log.info("基于预查询数据创建发票录入完成，发票ID: {}", invoiceCreateResult.getGeneratedDocumentId());
            return CompanyBusinessResponse.success("基于预查询数据创建发票录入成功", invoiceCreateResult.getGeneratedDocumentId());

        } catch (Exception e) {
            log.error("基于预查询数据创建发票录入异常，业务ID: {}", request.getBusinessId(), e);
            return CompanyBusinessResponse.failure("基于预查询数据创建发票录入异常: " + e.getMessage(), "INVOICE_CREATE_WITH_DATA_ERROR");
        }
    }


    @Override
    public CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request) {
        // 从 businessData 中获取发票ID
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        String invoiceId;
        if (businessData != null && businessData.containsKey("invoiceId")) {
            invoiceId = String.valueOf(businessData.get("invoiceId"));
        } else {
            invoiceId = null;
        }

        if (invoiceId == null) {
            return CompanyBusinessResponse.failure("发票ID为空，无法进行审核", "INVOICE_ID_MISSING");
        }

        // 配置业务操作 - 审核发票
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核发票")
                .apiPath("/api/v1/receiptInvoice/approve.do")
                .dataPreparer(req -> prepareInvoiceApproveData(req, invoiceId))
                .resultExtractor(this::extractApproveResult);

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    // ========== 私有业务方法 ==========

    /**
     * 创建发票（内部方法）
     */
    private CompanyBusinessResponse createInvoice(CompanyBusinessRequest request,
                                                  Map<String, Object> invoiceDetailData) {
        request.setUserName("admin");
        // 配置业务操作 - 创建发票
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("创建发票")
                .apiPath("/api/v1/receiptInvoice/create.do")
                .dataPreparer(req -> prepareInvoiceCreateData(req, invoiceDetailData))
                .resultExtractor(this::extractInvoiceId);

        // 设置目标公司为源公司（给上一家公司录票）
        // 注意：这里直接修改原请求对象，与原 InvoiceEntryFlow 逻辑保持一致
        String originalTargetCompany = request.getTargetCompanyCode();
        request.setTargetCompanyCode(request.getSourceCompanyCode());

        // 直接调用，异常处理由模板统一处理
        CompanyBusinessResponse result = universalActivityTemplate.execute(request, config);

        // 恢复原始目标公司设置
        request.setTargetCompanyCode(originalTargetCompany);

        return result;
    }

    /**
     * 审核发票（内部方法，带发票ID参数）
     */
    private CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request, 
                                                  CompanyBusinessResponse invoiceCreateResult) {
        String invoiceId = invoiceCreateResult.getGeneratedDocumentId();
        if (invoiceId == null) {
            return CompanyBusinessResponse.failure("发票ID为空，无法进行审核", "INVOICE_ID_MISSING");
        }

        // 配置业务操作 - 审核发票
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("自动审核发票")
                .apiPath("/api/v1/receiptInvoice/approve.do")
                .dataPreparer(req -> prepareInvoiceApproveData(req, invoiceId))
                .resultExtractor(this::extractApproveResult);

        // 直接调用，异常处理由模板统一处理
        return universalActivityTemplate.execute(request, config);
    }

    // ========== 私有数据准备方法 ==========

    /**
     * 准备发票详情查询数据
     */
    private Map<String, Object> prepareInvoiceDetailQueryData(CompanyBusinessRequest request) {
        Map<String, Object> queryData = new HashMap<>();

        // 从扩展属性中获取销售单号
        String saleOrderNo = getSaleOrderNoFromExtendedProperties(request);
        if (saleOrderNo != null) {
            queryData.put("saleorderNo", saleOrderNo);
        }

        log.debug("准备发票详情查询数据完成，业务ID: {}, 销售单号: {}", 
                request.getBusinessId(), saleOrderNo);
        return queryData;
    }

    /**
     * 准备发票创建数据
     */
    private Map<String, Object> prepareInvoiceCreateData(CompanyBusinessRequest request,
                                                         Map<String, Object> invoiceDetailData) {
        Map<String, Object> createData = new HashMap<>();

        // 处理与InvoiceEntryFlow完全一致的嵌套数据结构 (第107-111行逻辑)
        Map<String, Object> data = (Map<String, Object>) invoiceDetailData.get("data");
        if (data != null) {
            // 组装录票字段 (第119-121行逻辑)
            createData.put("invoiceNo", data.get("invoiceNo"));
            createData.put("invoiceGoods", data.get("invoiceGoods"));
        }

        // 从扩展属性中获取采购单号
        String buyOrderNo = getBuyOrderNoFromExtendedProperties(request);
        if (buyOrderNo != null) {
            createData.put("buyOrderNo", buyOrderNo);
        }

        log.info("准备发票创建数据完成，业务ID: {},入参：{}", request.getBusinessId(), JSON.toJSON(createData));
        return createData;
    }

    /**
     * 准备发票审核数据
     */
    private Map<String, Object> prepareInvoiceApproveData(CompanyBusinessRequest request, String invoiceId) {
        Map<String, Object> approveData = new HashMap<>();

        approveData.put("invoiceId", invoiceId);
        approveData.put("approveResult", "approved");
        approveData.put("approveComment", "系统自动审核通过");
        approveData.put("source", "TEMPORAL_WORKFLOW");
        approveData.put("approveTime", System.currentTimeMillis());

        log.debug("准备发票审核数据完成，业务ID: {}, 发票ID: {}", 
                request.getBusinessId(), invoiceId);
        return approveData;
    }

    // ========== 辅助方法 ==========

    /**
     * 从扩展属性中获取销售单号
     */
    private String getSaleOrderNoFromExtendedProperties(CompanyBusinessRequest request) {
        if (request.getExtendedProperties() != null) {
            return (String) request.getExtendedProperties().get("saleOrderNo");
        }
        return null;
    }

    /**
     * 从扩展属性中获取采购单号（修正为String类型，与InvoiceEntryFlow保持一致）
     */
    private String getBuyOrderNoFromExtendedProperties(CompanyBusinessRequest request) {
        if (request.getExtendedProperties() != null) {
            return (String) request.getExtendedProperties().get("buyOrderNo");
        }
        return null;
    }

    /**
     * 从创建结果中提取发票ID
     * 迁移自 InvoiceEntryFlow.extractInvoiceId 方法，保持逻辑完全一致
     */
    private String extractInvoiceId(Map<String, Object> createResult) {
        if (createResult == null) {
            return null;
        }

        // 尝试多种可能的字段名
        Object invoiceId = createResult.get("invoiceId");
        if (invoiceId == null) {
            invoiceId = createResult.get("id");
        }
        if (invoiceId == null) {
            invoiceId = createResult.get("data");
        }

        return invoiceId != null ? invoiceId.toString() : null;
    }

    /**
     * 从审核结果中提取审核状态
     * 迁移自 InvoiceEntryFlow.extractApproveResult 方法，保持逻辑完全一致
     */
    private String extractApproveResult(Map<String, Object> approveResult) {
        if (approveResult == null) {
            return "false";
        }

        // 尝试多种可能的字段名
        Object success = approveResult.get("success");
        if (success == null) {
            success = approveResult.get("result");
        }
        if (success == null) {
            success = approveResult.get("approved");
        }

        if (success instanceof Boolean) {
            return success.toString();
        } else if (success instanceof String) {
            String successStr = (String) success;
            return ("true".equalsIgnoreCase(successStr) || "success".equalsIgnoreCase(successStr)) ? "true" : "false";
        }

        return "false";
    }

    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }
}
