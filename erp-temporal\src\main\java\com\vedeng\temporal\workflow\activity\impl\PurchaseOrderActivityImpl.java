package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import com.vedeng.temporal.mapper.TemporalBaseCompanyInfoMapper;
import com.vedeng.temporal.workflow.activity.PurchaseOrderActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import com.vedeng.temporal.workflow.activity.dto.PurchaseOrderUpdateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购订单 Activity 实现类 - 极简版
 * 
 * 架构优化说明：
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 *
 * 业务流程：
 * 1. createPurchaseOrder - 创建采购订单，返回订单ID
 * 2. submitPurchaseOrder - 提交审核，需要订单ID
 * 3. approvePurchaseOrder - 审核通过，需要订单ID
 * 4. queryPurchaseOrderStatus - 查询状态，用于流程控制
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (极简化架构)
 * @since 2025-01-18
 */
@Component
@Slf4j
public class PurchaseOrderActivityImpl implements PurchaseOrderActivity {
    
    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;
    
    @Autowired
    private TemporalFlowOrderMapper temporalFlowOrderMapper;
    
    @Autowired  
    private TemporalBaseCompanyInfoMapper temporalBaseCompanyInfoMapper;
    
    @Override
    public CompanyBusinessResponse createPurchaseOrder(CompanyBusinessRequest request) {
        request.setUserName("eva.shao");
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("创建采购订单")
                .apiPath("/api/v1/buyorder/create.do")
                .dataPreparer(this::preparePurchaseOrderData)
                .resultExtractor(result -> {
                    if (result.containsKey("buyorderId")) {
                        return (String) result.get("buyorderId");
                    }
                    return null;
                });

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse updatePurchaseOrder(CompanyBusinessRequest request) {
        request.setUserName("eva.shao");
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
                UniversalActivityTemplate.BusinessOperationConfig.create()
                        .operationName("更新采购订单")
                        .apiPath("/api/v1/buyorder/update.do")
                        .dataPreparer(this::prepareUpdateData)
                        .resultExtractor(result -> "SUCCESS"); // 更新操作不需要返回特定ID

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }
    
    @Override
    public CompanyBusinessResponse submitPurchaseOrderForApproval(CompanyBusinessRequest request) {
        request.setUserName("eva.shao");
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("提交采购订单审核")
                .apiPath("/api/v1/buyorder/submit.do")
                .dataPreparer(this::prepareSubmitData); // 提交操作不需要返回特定ID

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }

    @Override
    public CompanyBusinessResponse approvePurchaseOrder(CompanyBusinessRequest request) {
        request.setUserName("eva.shao");
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核采购订单")
                .apiPath("/api/v1/buyorder/approve.do")
                .dataPreparer(this::prepareApproveData); // 审核操作不需要返回特定ID

        // 使用异常优先模式，失败时直接抛出异常
        return universalActivityTemplate.execute(request, config);
    }
    

    
    // ========== 私有数据准备方法 ==========
    
    /**
     * 准备采购订单创建数据
     */
    private Map<String, Object> preparePurchaseOrderData(CompanyBusinessRequest request) {
        Map<String, Object> orderData = new HashMap<>();
        // 从 businessData 中提取业务特定数据（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        // 添加其他必要字段
        orderData.put("saleorderId", businessData.get("saleorderId"));
        log.debug("准备采购订单创建数据完成, 业务ID: {}", request.getBusinessId());
        return orderData;
    }
    
    /**
     * 准备提交审核数据
     */
    private Map<String, Object> prepareSubmitData(CompanyBusinessRequest request) {
        Map<String, Object> submitData = new HashMap<>();// 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Object orderId = businessData.get("orderId");
            submitData.put("buyorderId", orderId);
        }
        log.debug("准备提交审核数据完成, 业务ID: {}", request.getBusinessId());
        return submitData;
    }
    
    /**
     * 准备审核数据
     */
    private Map<String, Object> prepareApproveData(CompanyBusinessRequest request) {
        Map<String, Object> approveData = new HashMap<>();
        
        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Object orderId = businessData.get("orderId");
            approveData.put("purchaseOrderId", orderId);
            approveData.put("orderId", orderId);
        }
        
        approveData.put("approveTime", System.currentTimeMillis());
        approveData.put("approveResult", "APPROVED");
        approveData.put("approveReason", "流程自动审核");
        
        log.debug("准备审核数据完成, 业务ID: {}", request.getBusinessId());
        return approveData;
    }
    
    /**
     * 准备查询数据
     */
    private Map<String, Object> prepareQueryData(CompanyBusinessRequest request) {
        Map<String, Object> queryData = new HashMap<>();
        
        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            queryData.put("orderId", businessData.get("orderId"));
            queryData.put("purchaseOrderId", businessData.get("orderId"));
        }
        
        log.debug("准备查询数据完成, 业务ID: {}", request.getBusinessId());
        return queryData;
    }
    
    /**
     * 准备更新数据 - 统一从业务流转单构建完整数据
     */
    private PurchaseOrderUpdateRequest prepareUpdateData(CompanyBusinessRequest request) {
        log.info("从业务流转单构建完整的采购订单更新请求, 业务ID: {}", request.getBusinessId());
        return buildCompleteUpdateRequest(request);
    }
    
    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (StrUtil.isBlank(businessDataJson)) {
            return new HashMap<>();
        }
        
        try {
            return JSONUtil.toBean(businessDataJson, Map.class);
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }
    
    // ========== 第二阶段：数据构建方法 ==========
    
    /**
     * 从业务流转单构建完整的更新请求
     */
    private PurchaseOrderUpdateRequest buildCompleteUpdateRequest(CompanyBusinessRequest request) {
        try {
            // 1. 获取关键标识信息
            Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
            String flowOrderId = request.getBusinessId();
            String orderId = (String) businessData.get("orderId");
            String targetCompany = request.getTargetCompanyCode();
            
            log.info("构建完整更新请求 - flowOrderId: {}, orderId: {}, targetCompany: {}", 
                    flowOrderId, orderId, targetCompany);
            
            // 2. 查询业务流转单基础信息
            FlowOrderInfo flowOrder = queryFlowOrderInfo(flowOrderId);
            
            // 3. 查询供应商信息
            SupplierInfo supplier = querySupplierInfo(targetCompany, flowOrderId);
            
            // 4. 查询商品列表和价格信息
            List<GoodsInfo> goodsList = queryGoodsListWithPrice(flowOrderId, targetCompany);
            
            // 5. 组装完整的 PurchaseOrderUpdateRequest
            return assemblePurchaseOrderUpdateRequest(orderId, flowOrder, supplier, goodsList);
            
        } catch (Exception e) {
            log.error("构建完整更新请求失败, 业务ID: {}", request.getBusinessId(), e);
            throw new RuntimeException("构建采购订单更新请求失败", e);
        }
    }
    
    // ========== 第二阶段：数据查询方法 ==========
    
    /**
     * 查询业务流转单基础信息
     */
    private FlowOrderInfo queryFlowOrderInfo(String flowOrderId) {
        if (StrUtil.isBlank(flowOrderId)) {
            throw new IllegalArgumentException("流转单ID不能为空");
        }
        
        Long flowOrderIdLong;
        try {
            flowOrderIdLong = Long.valueOf(flowOrderId);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("流转单ID格式无效: " + flowOrderId, e);
        }
        
        // 使用 Mapper 查询完整业务流转单信息
        Map<String, Object> completeInfo = temporalFlowOrderMapper.selectFlowOrderCompleteInfo(flowOrderIdLong);
        
        if (completeInfo == null) {
            throw new IllegalStateException("未找到流转单信息: " + flowOrderId);
        }
        
        FlowOrderInfo info = new FlowOrderInfo();
        info.setFlowOrderId(flowOrderId);
        info.setFlowOrderNo((String) completeInfo.get("FLOW_ORDER_NO"));
        info.setBaseOrderId((String) completeInfo.get("BASE_ORDER_ID"));
        info.setBaseOrderNo((String) completeInfo.get("BASE_ORDER_NO"));
        info.setCreator((String) completeInfo.get("CREATOR"));
        info.setCreatorName((String) completeInfo.get("CREATOR_NAME"));
        
        // 验证关键字段
        if (StrUtil.isBlank(info.getFlowOrderNo())) {
            throw new IllegalStateException("流转单编号为空: " + flowOrderId);
        }
        
        log.debug("查询业务流转单信息成功 - flowOrderId: {}, flowOrderNo: {}", 
                flowOrderId, info.getFlowOrderNo());
        
        return info;
    }
    
    /**
     * 查询供应商信息
     */
    private SupplierInfo querySupplierInfo(String companyCode, String flowOrderId) {
        if (StrUtil.isBlank(companyCode)) {
            throw new IllegalArgumentException("公司代码不能为空");
        }
        
        if (temporalBaseCompanyInfoMapper == null) {
            throw new IllegalStateException("BaseCompanyInfoMapper 未正确注入，请检查Spring配置");
        }
        
        SupplierInfo info = new SupplierInfo();
        
        // 根据公司简称查询公司信息
        BaseCompanyInfoEntity companyInfo = temporalBaseCompanyInfoMapper.selectByShortName(companyCode);
        
        if (companyInfo == null) {
            throw new IllegalStateException("未找到公司信息: " + companyCode);
        }
        
        // 设置供应商信息
        if (companyInfo.getSupplierTraderId() != null) {
            info.setTraderId(companyInfo.getSupplierTraderId().longValue());
        }
        
        if (StrUtil.isBlank(companyInfo.getCompanyName())) {
            throw new IllegalStateException("公司名称为空: " + companyCode);
        }
        
        info.setTraderName(companyInfo.getCompanyName());
        
        log.debug("查询到供应商信息 - companyCode: {}, traderId: {}, traderName: {}", 
                companyCode, info.getTraderId(), info.getTraderName());
        
        return info;
    }
    
    /**
     * 查询商品列表和价格信息
     */
    private List<GoodsInfo> queryGoodsListWithPrice(String flowOrderId, String companyCode) {
        if (StrUtil.isBlank(flowOrderId)) {
            throw new IllegalArgumentException("流转单ID不能为空");
        }
        
        if (StrUtil.isBlank(companyCode)) {
            throw new IllegalArgumentException("公司代码不能为空");
        }
        
        Long flowOrderIdLong;
        try {
            flowOrderIdLong = Long.valueOf(flowOrderId);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("流转单ID格式无效: " + flowOrderId, e);
        }
        
        List<GoodsInfo> goodsList = new ArrayList<>();
        
        // 1. 查询商品明细信息
        List<Map<String, Object>> details = temporalFlowOrderMapper.selectFlowOrderDetails(flowOrderIdLong);
        
        if (details == null || details.isEmpty()) {
            throw new IllegalStateException("未找到商品明细信息 - flowOrderId: " + flowOrderId);
        }
        
        // 2. 查询价格信息
        List<Map<String, Object>> prices = temporalFlowOrderMapper.selectFlowNodePrices(flowOrderIdLong, companyCode);
        
        if (prices == null || prices.isEmpty()) {
            throw new IllegalStateException("未找到价格信息 - flowOrderId: " + flowOrderId + ", companyCode: " + companyCode);
        }
        
        // 3. 将价格信息转换为 Map，便于查找
        Map<String, Map<String, Object>> priceMap = new HashMap<>();
        for (Map<String, Object> price : prices) {
            String skuId = (String) price.get("SKU_ID");
            if (skuId != null) {
                priceMap.put(skuId, price);
            }
        }
        
        // 4. 合并商品明细和价格信息
        for (Map<String, Object> detail : details) {
            GoodsInfo goodsInfo = new GoodsInfo();
            
            // 基础商品信息
            String skuId = (String) detail.get("SKU_ID");
            if (StrUtil.isBlank(skuId)) {
                throw new IllegalStateException("商品SKU_ID为空");
            }
            
            goodsInfo.setSkuId(skuId);
            goodsInfo.setSkuNo((String) detail.get("SKU_NO"));
            goodsInfo.setProductName((String) detail.get("PRODUCT_NAME"));
            goodsInfo.setBrand((String) detail.get("BRAND"));
            goodsInfo.setModel((String) detail.get("MODEL"));
            goodsInfo.setUnit((String) detail.get("UNIT"));
            
            // 验证商品名称
            if (StrUtil.isBlank(goodsInfo.getProductName())) {
                throw new IllegalStateException("商品名称为空 - skuId: " + skuId);
            }
            
            // 数量信息
            Object quantityObj = detail.get("QUANTITY");
            if (quantityObj != null) {
                goodsInfo.setQuantity(Integer.valueOf(quantityObj.toString()));
            } else {
                throw new IllegalStateException("商品数量为空 - skuId: " + skuId);
            }
            
            // 价格信息（从价格Map中获取）
            if (!priceMap.containsKey(skuId)) {
                throw new IllegalStateException("未找到商品价格信息 - skuId: " + skuId + ", companyCode: " + companyCode);
            }
            
            Map<String, Object> priceInfo = priceMap.get(skuId);
            
            Object priceObj = priceInfo.get("PRICE");
            if (priceObj != null) {
                goodsInfo.setPrice(Integer.valueOf(priceObj.toString()));
            } else {
                throw new IllegalStateException("商品价格为空 - skuId: " + skuId);
            }
            
            Object markupRateObj = priceInfo.get("MARKUP_RATE");
            if (markupRateObj != null) {
                goodsInfo.setMarkupRate(Integer.valueOf(markupRateObj.toString()));
            }
            
            Object grossProfitRateObj = priceInfo.get("GROSS_PROFIT_RATE");
            if (grossProfitRateObj != null) {
                goodsInfo.setGrossProfitRate(Integer.valueOf(grossProfitRateObj.toString()));
            }
            
            goodsList.add(goodsInfo);
        }
        
        if (goodsList.isEmpty()) {
            throw new IllegalStateException("处理后的商品列表为空 - flowOrderId: " + flowOrderId);
        }
        
        log.info("查询商品列表和价格完成 - flowOrderId: {}, companyCode: {}, 商品数量: {}", 
                flowOrderId, companyCode, goodsList.size());
        
        return goodsList;
    }
    
    // ========== 第四阶段：数据组装方法 ==========
    
    /**
     * 组装完整的采购订单更新请求
     */
    private PurchaseOrderUpdateRequest assemblePurchaseOrderUpdateRequest(
            String orderId, FlowOrderInfo flowOrder, SupplierInfo supplier, List<GoodsInfo> goodsList) {
        
        PurchaseOrderUpdateRequest request = new PurchaseOrderUpdateRequest();
        
        // 基础订单信息
        if (StrUtil.isNotBlank(orderId)) {
            request.setBuyorderId(Long.valueOf(orderId));
        }
        request.setIsNew(0); // 更新操作
        
        // 流转单相关信息
        if (flowOrder.getFlowOrderNo() != null) {
            request.setBuyorderNo(flowOrder.getFlowOrderNo()); // 采购订单号
        }
        
        // 供应商信息
        if (supplier.getTraderId() != null) {
            request.setTraderId(supplier.getTraderId());
        }
        request.setTraderName(supplier.getTraderName());
        
        // 供应商备注信息
        if (supplier.getComments() != null) {
            request.setTraderComments(supplier.getComments());
        }
        
        // 商品列表
        request.setGoodsList(convertToCompleteGoodsList(goodsList));
        
        // 应用业务规则和默认值
        applyBusinessRules(request, flowOrder);
        
        // 验证数据完整性
        validateUpdateRequest(request);
        
        log.info("组装完整更新请求完成 - orderId: {}, supplier: {}, goods count: {}, flowOrderNo: {}", 
                orderId, supplier.getTraderName(), goodsList.size(), flowOrder.getFlowOrderNo());
        
        return request;
    }
    
    /**
     * 转换商品列表 - 完整字段映射
     */
    private List<PurchaseOrderUpdateRequest.PurchaseOrderGoods> convertToCompleteGoodsList(List<GoodsInfo> goodsList) {
        return goodsList.stream().map(goods -> {
            PurchaseOrderUpdateRequest.PurchaseOrderGoods orderGoods = 
                new PurchaseOrderUpdateRequest.PurchaseOrderGoods();
                
            // 基础商品信息
            orderGoods.setGoodsName(goods.getProductName());
            
            // 价格相关字段
            if (goods.getPrice() != null) {
                orderGoods.setActualPurchasePrice(goods.getPrice());
            }
            
            // 数量信息（转换为字符串格式）
            if (goods.getQuantity() != null) {
                orderGoods.setRebateNum(goods.getQuantity().toString());
            }
            
            log.debug("商品映射完成 - 商品名称: {}, 价格: {}, 数量: {}", 
                    goods.getProductName(), goods.getPrice(), goods.getQuantity());
                    
            return orderGoods;
        }).collect(Collectors.toList());
    }
    
    // ========== 第五阶段：业务规则和验证方法 ==========
    
    /**
     * 应用业务规则和默认值
     */
    private void applyBusinessRules(PurchaseOrderUpdateRequest request, FlowOrderInfo flowOrder) {
        // 1. 付款方式规则：先货后款，预付0%
        request.setPaymentType(1);
        request.setPrepaidAmount("0");
        
        // 2. 收票种类：13%增值税专用发票  
        request.setInvoiceType(972);
        
        // 3. 运费说明：合同总额包含运费，送货上门
        request.setFreightDescription(470);
        
        // 4. 其他默认值
        request.setIsGift(0); // 非赠品
        request.setDeliveryDirect(0); // 非直发

        log.debug("应用业务规则完成 - paymentType: {}, invoiceType: {}",
                request.getPaymentType(), request.getInvoiceType());
    }
    
    /**
     * 验证更新请求数据
     */
    private void validateUpdateRequest(PurchaseOrderUpdateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("更新请求不能为空");
        }
        
        // 验证必填字段
        if (request.getBuyorderId() == null) {
            throw new IllegalArgumentException("采购订单ID不能为空");
        }
        
        if (StrUtil.isBlank(request.getTraderName())) {
            throw new IllegalArgumentException("供应商名称不能为空");
        }
        
        // 验证商品列表
        if (request.getGoodsList() != null) {
            for (PurchaseOrderUpdateRequest.PurchaseOrderGoods goods : request.getGoodsList()) {
                if (StrUtil.isBlank(goods.getGoodsName())) {
                    log.warn("发现商品名称为空的商品项");
                }
            }
        }
        
        log.debug("更新请求数据验证通过 - orderId: {}, supplier: {}, goods count: {}", 
                request.getBuyorderId(), request.getTraderName(), 
                request.getGoodsList() != null ? request.getGoodsList().size() : 0);
    }
    
    // ========== 内部数据传输对象 ==========
    
    /**
     * 业务流转单信息
     */
    private static class FlowOrderInfo {
        private String flowOrderId;
        private String flowOrderNo;      // 新增：流转单编号
        private String baseOrderId;      // 新增：基础订单ID
        private String baseOrderNo;      // 新增：基础订单编号
        private String creator;
        private String creatorName;      // 新增：创建人姓名
        
        public String getFlowOrderId() { return flowOrderId; }
        public void setFlowOrderId(String flowOrderId) { this.flowOrderId = flowOrderId; }
        
        public String getFlowOrderNo() { return flowOrderNo; }
        public void setFlowOrderNo(String flowOrderNo) { this.flowOrderNo = flowOrderNo; }
        
        public String getBaseOrderId() { return baseOrderId; }
        public void setBaseOrderId(String baseOrderId) { this.baseOrderId = baseOrderId; }
        
        public String getBaseOrderNo() { return baseOrderNo; }
        public void setBaseOrderNo(String baseOrderNo) { this.baseOrderNo = baseOrderNo; }
        
        public String getCreator() { return creator; }
        public void setCreator(String creator) { this.creator = creator; }
        
        public String getCreatorName() { return creatorName; }
        public void setCreatorName(String creatorName) { this.creatorName = creatorName; }
    }
    
    /**
     * 供应商信息
     */
    private static class SupplierInfo {
        private Long traderId;
        private String traderName;
        private String comments;         // 新增：备注信息
        
        public Long getTraderId() { return traderId; }
        public void setTraderId(Long traderId) { this.traderId = traderId; }
        
        public String getTraderName() { return traderName; }
        public void setTraderName(String traderName) { this.traderName = traderName; }
        
        public String getComments() { return comments; }
        public void setComments(String comments) { this.comments = comments; }
    }
    
    /**
     * 商品信息 - 扩展版本
     */
    private static class GoodsInfo {
        private String skuId;            // 新增：SKU ID
        private String skuNo;            // 新增：SKU 编号
        private String productName;
        private String brand;            // 新增：品牌
        private String model;            // 新增：型号
        private String unit;             // 新增：单位
        private Integer quantity;        // 新增：数量
        private Integer price;
        private Integer markupRate;      // 新增：加价率
        private Integer grossProfitRate; // 新增：毛利率
        
        public String getSkuId() { return skuId; }
        public void setSkuId(String skuId) { this.skuId = skuId; }
        
        public String getSkuNo() { return skuNo; }
        public void setSkuNo(String skuNo) { this.skuNo = skuNo; }
        
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        
        public String getBrand() { return brand; }
        public void setBrand(String brand) { this.brand = brand; }
        
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        
        public Integer getPrice() { return price; }
        public void setPrice(Integer price) { this.price = price; }
        
        public Integer getMarkupRate() { return markupRate; }
        public void setMarkupRate(Integer markupRate) { this.markupRate = markupRate; }
        
        public Integer getGrossProfitRate() { return grossProfitRate; }
        public void setGrossProfitRate(Integer grossProfitRate) { this.grossProfitRate = grossProfitRate; }
    }
}
