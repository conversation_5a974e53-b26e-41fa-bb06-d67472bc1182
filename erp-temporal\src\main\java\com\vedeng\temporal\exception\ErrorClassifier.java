package com.vedeng.temporal.exception;

import com.vedeng.temporal.enums.BusinessStepType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 错误分类工具类
 * 
 * 负责判断错误类型和重试策略：
 * 1. 技术异常：网络、超时、服务器错误等，应该重试
 * 2. 业务异常：验证、权限、业务规则等，不应该重试
 * 3. 关键步骤：失败时必须终止流程的步骤
 * 4. 可选步骤：失败时可以继续执行的步骤
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (支持新异常体系)
 * @since 2025-01-18
 */
@Component
@Slf4j
public class ErrorClassifier {
    
    // ========== 技术异常前缀 ==========
    private static final Set<String> RETRYABLE_ERROR_PREFIXES = new HashSet<>(Arrays.asList(
        "NETWORK_",         // 网络异常
        "TIMEOUT_",         // 超时异常
        "SERVER_",          // 服务器异常
        "DATABASE_",        // 数据库异常
        "CONNECTION_",      // 连接异常
        "SERVICE_",         // 服务异常
        "SYSTEM_",          // 系统异常
        "INFRASTRUCTURE_",  // 基础设施异常
        "POLLING_",         // 轮询技术异常
        "API_CALL_",        // API 调用技术异常
        "ACTIVITY_"         // Activity 层技术异常
    ));
    
    // ========== 业务异常前缀 ==========
    private static final Set<String> NON_RETRYABLE_ERROR_PREFIXES = new HashSet<>(Arrays.asList(
        "VALIDATION_",      // 数据验证异常
        "BUSINESS_RULE_",   // 业务规则异常
        "PERMISSION_",      // 权限异常
        "DATA_NOT_FOUND",   // 数据不存在
        "APPROVAL_",        // 审批相关异常
        "CONFIGURATION_",   // 配置异常
        "DUPLICATE_",       // 重复数据异常
        "INSUFFICIENT_",    // 资源不足异常
        "EXPIRED_",         // 过期异常
        "INVALID_"          // 无效数据异常
    ));
    
    // ========== 关键步骤类型 ==========
    private static final Set<BusinessStepType> CRITICAL_STEP_TYPES = new HashSet<>(Arrays.asList(
        BusinessStepType.SALES_ORDER,      // 销售订单是关键步骤
        BusinessStepType.PURCHASE_ORDER    // 采购订单是关键步骤
    ));
    
    // ========== 可选步骤类型 ==========
    private static final Set<BusinessStepType> OPTIONAL_STEP_TYPES = new HashSet<>(Arrays.asList(
        BusinessStepType.INVOICE_ENTRY,    // 发票录入可选
        BusinessStepType.SALES_INVOICE     // 销售发票可选
    ));
    
    /**
     * 统一的异常分类入口（增强版）
     * 支持异常对象和错误码双重分类，并考虑操作阶段信息
     * 
     * @param originalException 原始异常对象
     * @param errorCode 错误码（可以为null）
     * @param operationPhase 操作阶段（可以为null）
     * @return 完整的异常分类结果
     */
    public ExceptionClassification classifyException(Exception originalException, String errorCode, String operationPhase) {
        if (originalException == null) {
            log.warn("原始异常为null，使用默认分类");
            return new ExceptionClassification(false, "NULL_EXCEPTION", ExceptionCategory.UNKNOWN, operationPhase);
        }
        
        // 1. 基于异常对象的分类
        Boolean retryableByException = isRetryableByExceptionType(originalException);
        ExceptionCategory category = detectExceptionCategory(originalException, errorCode, operationPhase);
        
        // 2. 基于错误码的分类
        boolean retryableByErrorCode = isRetryableByErrorCode(errorCode);
        
        // 3. 基于操作阶段的特殊处理
        Boolean retryableByPhase = isRetryableByOperationPhase(operationPhase, originalException);
        
        // 4. 综合判断（优先级：操作阶段 > 异常类型 > 错误码）
        boolean finalRetryable = retryableByPhase != null ? retryableByPhase : 
                                (retryableByException != null ? retryableByException : retryableByErrorCode);
            
        // 5. 生成统一的错误码
        String finalErrorCode = generateStandardErrorCode(originalException, errorCode, category, operationPhase);
        
        log.debug("异常分类完成 - 异常类型: {}, 错误码: {}, 操作阶段: {}, 可重试: {}, 分类: {}", 
                originalException.getClass().getSimpleName(), finalErrorCode, operationPhase, finalRetryable, category);
        
        return new ExceptionClassification(finalRetryable, finalErrorCode, category, operationPhase);
    }
    
    /**
     * 基于异常类型判断是否可重试
     * 
     * @param e 异常对象
     * @return Boolean值，null表示无法从异常类型确定
     */
    private Boolean isRetryableByExceptionType(Exception e) {
        if (e == null) return null;
        
        // 网络相关异常通常可重试
        if (e instanceof java.net.SocketTimeoutException ||
            e instanceof java.net.ConnectException ||
            e instanceof java.net.SocketException) {
            log.debug("识别为网络异常，可重试: {}", e.getClass().getSimpleName());
            return true;
        }
        
        // 数据库相关异常通常可重试
        if (e.getClass().getName().contains("SQLException") ||
            e.getClass().getName().contains("DataAccessException")) {
            log.debug("识别为数据库异常，可重试: {}", e.getClass().getSimpleName());
            return true;
        }
        
        // 服务调用异常通常可重试
        if (e.getClass().getName().contains("RestClientException") ||
            e.getClass().getName().contains("HttpClientException")) {
            log.debug("识别为服务调用异常，可重试: {}", e.getClass().getSimpleName());
            return true;
        }
        
        // 业务逻辑异常通常不可重试
        if (e instanceof IllegalArgumentException ||
            e instanceof IllegalStateException) {
            log.debug("识别为业务逻辑异常，不重试: {}", e.getClass().getSimpleName());
            return false;
        }
        
        // 无法从异常类型确定，需要依赖错误码
        return null;
    }
    
    /**
     * 基于错误码判断是否可重试
     * 复用现有的isRetryableError逻辑
     */
    private boolean isRetryableByErrorCode(String errorCode) {
        return isRetryableError(errorCode);
    }
    
    /**
     * 检测异常类别（增强版，支持操作阶段）
     * 
     * @param exception 异常对象
     * @param errorCode 错误码
     * @param operationPhase 操作阶段
     * @return 异常类别
     */
    private ExceptionCategory detectExceptionCategory(Exception exception, String errorCode, String operationPhase) {
        if (exception == null) {
            return ExceptionCategory.UNKNOWN;
        }
        
        // 首先基于操作阶段进行分类
        ExceptionCategory phaseCategory = categorizeByOperationPhase(operationPhase, exception);
        if (phaseCategory != ExceptionCategory.UNKNOWN) {
            return phaseCategory;
        }
        
        // 然后使用原有的分类逻辑
        return detectExceptionCategory(exception, errorCode);
    }

    /**
     * 基于操作阶段判断是否可重试
     * 
     * @param operationPhase 操作阶段
     * @param exception 异常对象
     * @return Boolean值，null表示无法从操作阶段确定
     */
    private Boolean isRetryableByOperationPhase(String operationPhase, Exception exception) {
        if (operationPhase == null || operationPhase.trim().isEmpty()) {
            return null;
        }
        
        String upperPhase = operationPhase.toUpperCase();
        
        // 创建阶段的异常通常可重试（网络、系统问题）
        if (upperPhase.contains("CREATE") || upperPhase.contains("SUBMIT")) {
            // 但验证异常不应该重试
            if (exception instanceof IllegalArgumentException || 
                upperPhase.contains("VALIDATION")) {
                return false;
            }
            return true;
        }
        
        // 审核阶段的异常要谨慎重试
        if (upperPhase.contains("APPROVE") || upperPhase.contains("AUDIT")) {
            // 审核失败通常是业务逻辑问题，不应该重试
            if (upperPhase.contains("FAILED") || upperPhase.contains("REJECTED")) {
                return false;
            }
            // 审核过程中的技术异常可以重试
            return true;
        }
        
        // 查询阶段的异常通常可重试
        if (upperPhase.contains("QUERY") || upperPhase.contains("CHECK")) {
            return true;
        }
        
        // 无法从操作阶段确定
        return null;
    }

    /**
     * 基于操作阶段分类异常
     * 
     * @param operationPhase 操作阶段
     * @param exception 异常对象
     * @return 异常类别
     */
    private ExceptionCategory categorizeByOperationPhase(String operationPhase, Exception exception) {
        if (operationPhase == null || operationPhase.trim().isEmpty()) {
            return ExceptionCategory.UNKNOWN;
        }
        
        String upperPhase = operationPhase.toUpperCase();
        
        // 数据验证阶段
        if (upperPhase.contains("VALIDATION") || upperPhase.contains("CHECK")) {
            return ExceptionCategory.VALIDATION;
        }
        
        // 网络请求阶段
        if (upperPhase.contains("SUBMIT") || upperPhase.contains("SEND")) {
            return ExceptionCategory.NETWORK;
        }
        
        // 业务逻辑阶段
        if (upperPhase.contains("APPROVE") || upperPhase.contains("PROCESS")) {
            return ExceptionCategory.BUSINESS_LOGIC;
        }
        
        // 数据查询阶段
        if (upperPhase.contains("QUERY") || upperPhase.contains("FETCH")) {
            return ExceptionCategory.DATABASE;
        }
        
        return ExceptionCategory.UNKNOWN;
    }

    /**
     * 检测异常类别（原有方法，保持兼容性）
     */
    private ExceptionCategory detectExceptionCategory(Exception exception, String errorCode) {
        if (exception == null) {
            return ExceptionCategory.UNKNOWN;
        }
        
        // 基于异常类型的分类
        String exceptionName = exception.getClass().getName().toLowerCase();
        
        if (exceptionName.contains("socket") || exceptionName.contains("network") ||
            exceptionName.contains("connect") || exceptionName.contains("timeout")) {
            return ExceptionCategory.NETWORK;
        }
        
        if (exceptionName.contains("sql") || exceptionName.contains("database") ||
            exceptionName.contains("dataaccess")) {
            return ExceptionCategory.DATABASE;
        }
        
        if (exception instanceof IllegalArgumentException ||
            exception instanceof IllegalStateException ||
            exceptionName.contains("validation")) {
            return ExceptionCategory.VALIDATION;
        }
        
        // 基于错误码的分类
        if (errorCode != null) {
            String upperErrorCode = errorCode.toUpperCase();
            
            if (upperErrorCode.startsWith("NETWORK_") || upperErrorCode.startsWith("CONNECTION_")) {
                return ExceptionCategory.NETWORK;
            }
            
            if (upperErrorCode.startsWith("DATABASE_") || upperErrorCode.startsWith("SQL_")) {
                return ExceptionCategory.DATABASE;
            }
            
            if (upperErrorCode.startsWith("VALIDATION_") || upperErrorCode.startsWith("BUSINESS_RULE_")) {
                return ExceptionCategory.BUSINESS_LOGIC;
            }
            
            if (upperErrorCode.startsWith("SYSTEM_") || upperErrorCode.startsWith("INFRASTRUCTURE_")) {
                return ExceptionCategory.SYSTEM;
            }
        }
        
        return ExceptionCategory.UNKNOWN;
    }
    
    /**
     * 生成标准化错误码（增强版，支持操作阶段）
     * 
     * @param exception 异常对象
     * @param errorCode 现有错误码
     * @param category 异常类别
     * @param operationPhase 操作阶段
     * @return 标准化的错误码
     */
    private String generateStandardErrorCode(Exception exception, String errorCode, ExceptionCategory category, String operationPhase) {
        // 如果已有有效错误码，优先使用
        if (errorCode != null && !errorCode.trim().isEmpty() && 
            !"UNKNOWN_ERROR".equals(errorCode.toUpperCase())) {
            return errorCode;
        }
        
        // 基于异常类型、类别和操作阶段生成错误码
        return generateErrorCodeFromException(exception, category, operationPhase);
    }

    /**
     * 基于异常类型生成错误码（增强版，支持操作阶段）
     * 
     * @param exception 异常对象
     * @param category 异常类别
     * @param operationPhase 操作阶段
     * @return 生成的错误码
     */
    private String generateErrorCodeFromException(Exception exception, ExceptionCategory category, String operationPhase) {
        if (exception == null) {
            return "NULL_EXCEPTION";
        }
        
        String exceptionName = exception.getClass().getSimpleName().toUpperCase();
        
        // 根据异常类别生成前缀
        String prefix = category.getErrorPrefix();
        
        // 如果有操作阶段，添加阶段前缀
        if (operationPhase != null && !operationPhase.trim().isEmpty()) {
            prefix = operationPhase.toUpperCase() + "_" + prefix;
        }
        
        // 特殊异常类型的映射
        if (exceptionName.contains("TIMEOUT")) {
            return prefix + "TIMEOUT_ERROR";
        } else if (exceptionName.contains("CONNECTION")) {
            return prefix + "CONNECTION_ERROR";
        } else if (exceptionName.contains("SOCKET")) {
            return prefix + "NETWORK_ERROR";
        } else if (exceptionName.contains("SQL")) {
            return prefix + "DATABASE_ERROR";
        } else if (exceptionName.contains("VALIDATION")) {
            return prefix + "VALIDATION_ERROR";
        } else if (exceptionName.contains("ILLEGAL")) {
            return prefix + "BUSINESS_LOGIC_ERROR";
        } else {
            return prefix + exceptionName;
        }
    }
    
    /**
     * 异常分类结果（增强版，包含操作阶段）
     */
    public static class ExceptionClassification {
        private final boolean retryable;
        private final String errorCode;
        private final ExceptionCategory category;
        private final String operationPhase;
        
        public ExceptionClassification(boolean retryable, String errorCode, ExceptionCategory category, String operationPhase) {
            this.retryable = retryable;
            this.errorCode = errorCode;
            this.category = category;
            this.operationPhase = operationPhase;
        }
        
        // 兼容性构造函数
        public ExceptionClassification(boolean retryable, String errorCode, ExceptionCategory category) {
            this(retryable, errorCode, category, null);
        }
        
        public boolean isRetryable() {
            return retryable;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public ExceptionCategory getCategory() {
            return category;
        }
        
        public String getOperationPhase() {
            return operationPhase;
        }
        
        @Override
        public String toString() {
            return String.format("ExceptionClassification{retryable=%s, errorCode='%s', category=%s, operationPhase='%s'}", 
                    retryable, errorCode, category, operationPhase);
        }
    }
    
    /**
     * 统一的异常分类入口（兼容性方法）
     * 保持与现有代码的兼容性
     */
    public ExceptionClassification classifyException(Exception originalException, String errorCode) {
        return classifyException(originalException, errorCode, null);
    }
    
    /**
     * 异常类别枚举
     */
    public enum ExceptionCategory {
        NETWORK("NETWORK_"),
        DATABASE("DATABASE_"),
        BUSINESS_LOGIC("BUSINESS_RULE_"),
        VALIDATION("VALIDATION_"),
        SYSTEM("SYSTEM_"),
        UNKNOWN("UNKNOWN_");
        
        private final String errorPrefix;
        
        ExceptionCategory(String errorPrefix) {
            this.errorPrefix = errorPrefix;
        }
        
        public String getErrorPrefix() {
            return errorPrefix;
        }
    }
    
    /**
     * 判断错误是否可重试
     * 
     * @param errorCode 错误码
     * @return true 如果应该重试，false 否则
     */
    public boolean isRetryableError(String errorCode) {
        if (errorCode == null || errorCode.trim().isEmpty()) {
            log.warn("错误码为空，默认不重试");
            return false;
        }
        
        String upperErrorCode = errorCode.toUpperCase();
        
        // 检查技术异常前缀
        for (String prefix : RETRYABLE_ERROR_PREFIXES) {
            if (upperErrorCode.startsWith(prefix)) {
                log.debug("识别为技术异常，可重试: {}", errorCode);
                return true;
            }
        }
        
        // 检查业务异常前缀
        for (String prefix : NON_RETRYABLE_ERROR_PREFIXES) {
            if (upperErrorCode.startsWith(prefix)) {
                log.debug("识别为业务异常，不重试: {}", errorCode);
                return false;
            }
        }
        
        // 特殊错误码处理
        if (isSpecialRetryableError(upperErrorCode)) {
            log.debug("识别为特殊技术异常，可重试: {}", errorCode);
            return true;
        }
        
        if (isSpecialNonRetryableError(upperErrorCode)) {
            log.debug("识别为特殊业务异常，不重试: {}", errorCode);
            return false;
        }
        
        // 默认策略：保守不重试
        log.warn("未知错误码，默认不重试: {}", errorCode);
        return false;
    }
    
    /**
     * 判断是否为关键步骤
     * 关键步骤失败时必须终止整个流程
     * 
     * @param stepType 步骤类型
     * @return true 如果是关键步骤，false 否则
     */
    public boolean isCriticalStep(BusinessStepType stepType) {
        if (stepType == null) {
            return false;
        }
        
        boolean isCritical = CRITICAL_STEP_TYPES.contains(stepType);
        log.debug("步骤 {} 是否为关键步骤: {}", stepType, isCritical);
        return isCritical;
    }
    
    /**
     * 判断是否为可选步骤
     * 可选步骤失败时可以继续执行后续步骤
     * 
     * @param stepType 步骤类型
     * @return true 如果是可选步骤，false 否则
     */
    public boolean isOptionalStep(BusinessStepType stepType) {
        if (stepType == null) {
            return false;
        }
        
        boolean isOptional = OPTIONAL_STEP_TYPES.contains(stepType);
        log.debug("步骤 {} 是否为可选步骤: {}", stepType, isOptional);
        return isOptional;
    }
    
    /**
     * 根据异常判断业务处理策略
     * 
     * @param exception 业务异常
     * @param stepType 步骤类型
     * @return 处理策略
     */
    public ErrorHandlingStrategy getHandlingStrategy(BusinessProcessException exception, BusinessStepType stepType) {
        if (exception.isRetryable()) {
            // 技术异常，应该重试
            return ErrorHandlingStrategy.RETRY;
        }
        
        if (isCriticalStep(stepType)) {
            // 关键步骤的业务异常，终止流程
            return ErrorHandlingStrategy.TERMINATE;
        }
        
        if (isOptionalStep(stepType)) {
            // 可选步骤的业务异常，继续执行
            return ErrorHandlingStrategy.CONTINUE;
        }
        
        // 默认策略：记录并继续
        return ErrorHandlingStrategy.RECORD_AND_CONTINUE;
    }

    /**
     * 根据异常和错误码创建 BusinessProcessException（兼容方法）
     * 
     * @param originalException 原始异常
     * @param errorCode 错误码
     * @param layerName 层次名称（ACTIVITY, STEP, PROCESS）
     * @param contextName 上下文名称（活动名、步骤名、流程名）
     * @return 分类后的业务异常
     */
    public BusinessProcessException classifyException(Exception originalException, String errorCode, 
                                                    String layerName, String contextName) {
        // 使用新的统一分类方法
        ExceptionClassification classification = classifyException(originalException, errorCode);
        
        String message = originalException != null ? originalException.getMessage() : "未知异常";
        
        switch (layerName.toUpperCase()) {
            case "ACTIVITY":
                return BusinessProcessException.fromActivity(message, 
                    classification.getErrorCode(), classification.isRetryable(), contextName);
            case "STEP":
                return BusinessProcessException.fromStep(message, 
                    classification.getErrorCode(), classification.isRetryable(), contextName, null);
            case "PROCESS":
                return BusinessProcessException.fromProcess(message, 
                    classification.getErrorCode(), classification.isRetryable(), contextName);
            default:
                log.warn("未知的层次名称: {}, 使用默认处理", layerName);
                return BusinessProcessException.create(message, classification.getErrorCode(), 
                    classification.isRetryable());
        }
    }
    
    /**
     * 特殊可重试错误码判断
     */
    private boolean isSpecialRetryableError(String errorCode) {
        return errorCode.contains("TEMPORARY") ||
               errorCode.contains("RETRY") ||
               errorCode.contains("UNAVAILABLE") ||
               errorCode.equals("UNKNOWN_ERROR") ||
               errorCode.equals("STEP_EXECUTION_ERROR") ||
               errorCode.equals("ACTIVITY_EXECUTION_ERROR");  // 替换原来的 FLOW_EXECUTION_ERROR
    }
    
    /**
     * 特殊不可重试错误码判断
     */
    private boolean isSpecialNonRetryableError(String errorCode) {
        return errorCode.contains("PERMANENT") ||
               errorCode.contains("FORBIDDEN") ||
               errorCode.contains("UNAUTHORIZED") ||
               errorCode.contains("NOT_FOUND") ||
               errorCode.contains("CONFLICT") ||
               errorCode.contains("BAD_REQUEST");
    }
    
    /**
     * 错误处理策略枚举
     */
    public enum ErrorHandlingStrategy {
        RETRY,                  // 重试
        TERMINATE,              // 终止流程
        CONTINUE,               // 继续执行
        RECORD_AND_CONTINUE     // 记录并继续
    }
}
