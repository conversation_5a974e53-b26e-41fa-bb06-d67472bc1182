package com.vedeng.api.standard.adapter.peerlist.dto;

import com.vedeng.api.standard.core.exception.ApiStandardException;
import lombok.Data;

import java.io.Serializable;

/**
 * 直发同行单明细请求
 * 
 * <AUTHOR>
 */
@Data
public class PeerListDetailRequest implements Serializable {
    
    private String logisticsNo;
    
    /**
     * 采购订单的物流明细ID
     */
    private Integer expressDetailId;
    
    /**
     * SPU类型
     */
    private String spuType;
    
    /**
     * 联合序列
     */
    private Integer unionSequence;
    
    /**
     * 订货号
     */
    private String sku;
    
    /**
     * 产品名称
     */
    private String skuName;
    
    /**
     * 型号/规格
     */
    private String model;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 生产企业
     */
    private String productCompany;
    
    /**
     * 生产许可证号
     */
    private String productionLicence;
    
    /**
     * 注册证号
     */
    private String registerNumber;
    
    /**
     * 生产批次号/序列号
     */
    private String batchNumber;
    
    /**
     * 收货数量
     */
    private String arrivalCount;
    
    /**
     * 原始数量
     */
    private Integer oldNum;
    
    /**
     * 生产日期 yyyy-MM-dd
     */
    private String manufactureDateTime;
    
    /**
     * 失效日期 yyyy-MM-dd
     */
    private String invalidDateTime;
    
    /**
     * 是否纳入医疗器械目录
     */
    private String medicalInstrumentCatalogIncluded;
    
    /**
     * 是否管理维正码
     */
    private String isManageVedengCode;
    
    /**
     * 是否启用有效期
     */
    private String isEnableValidityPeriod;
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (arrivalCount == null || arrivalCount.isEmpty()) {
            throw ApiStandardException.serviceExecutionError("收货数量不能为空");
        }
        try {
            int count = Integer.parseInt(arrivalCount);
            if (count <= 0) {
                throw ApiStandardException.serviceExecutionError("收货数量必须大于0");
            }
        } catch (NumberFormatException e) {
            throw ApiStandardException.serviceExecutionError("收货数量格式不正确");
        }
    }
} 
