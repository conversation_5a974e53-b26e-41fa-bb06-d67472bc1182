package com.vedeng.api.standard.adapter.express;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.api.standard.adapter.express.dto.ExpressCreateRequest;
import com.vedeng.api.standard.adapter.express.dto.ExpressQueryRequest;
import com.vedeng.api.standard.adapter.express.dto.ExpressRequest;
import com.vedeng.api.standard.adapter.express.dto.ExpressResponse;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.AddExpressEnableRule;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.vo.BuyorderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component("expressServiceAdapter")
public class ExpressServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query"};
    }

    private Object executeQueryOperation(ApiRequest apiRequest) {
        ExpressQueryRequest expressRequest = new ExpressQueryRequest();
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);
        try {
            return businessTemplate.<ExpressQueryRequest, ExpressResponse>executeCreate(apiRequest)
                    .requestType(ExpressQueryRequest.class)
                    .controller("buyorderApiServiceImpl", "getExpressGoodsList")
                    .withoutHttpParameters(
                            ParameterConfig.of(String.class, expressRequest.getBuyOrderNo())
                    )
                    .execute();
        } catch (Exception e) {
            throw new RuntimeException("新增快递:失败: " + e.getMessage(), e);
        }
    }

    private Object executeCreateOperation(ApiRequest apiRequest) {
        ExpressCreateRequest expressRequest = new ExpressCreateRequest();
        logger.info("apiRequest", JSONUtil.toJsonStr(apiRequest.getData()));
        BeanUtil.fillBeanWithMap(apiRequest.getData(), expressRequest, true);
        
        try {
            return businessTemplate.<ExpressCreateRequest, ExpressResponse>executeCreate(apiRequest)
                    .requestType(ExpressCreateRequest.class)
                    .responseType(ExpressResponse.class)
                    .validationRules(AddExpressEnableRule.class)
                    .controller("buyorderController", "saveAddExpress")
                    .withIdempotencyHandling("EXPRESS_CREATE")
                    .withoutHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.AMOUNT, BigDecimal.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.EXPRESS, Express.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.DELIVERY_TIMES, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.ID_NUM_PRICE, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.ID_SEND_N_SENDED_N_SUM_N, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.BUYORDER_VO, BuyorderVo.class)
                            
                    )
                    .execute();
        } catch (Exception e) {
            throw ApiStandardException.serviceExecutionError("新增快递失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getModuleName() {
        return "express";
    }
}
