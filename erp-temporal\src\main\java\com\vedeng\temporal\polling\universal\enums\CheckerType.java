package com.vedeng.temporal.polling.universal.enums;

/**
 * 完成条件检查器类型枚举
 * 
 * 定义了统一轮询组件支持的各种完成条件检查类型。
 * 通过配置化的方式替代函数式接口，确保序列化安全和Temporal确定性。
 * 
 * 设计原则：
 * - 每个类型对应一种常见的检查逻辑
 * - 通过参数配置具体的检查条件
 * - 保持类型安全和确定性
 * - 易于扩展新的检查类型
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
public enum CheckerType {
    
    /**
     * 字段等于指定值
     * 参数：fieldPath (字段路径), expectedValue (期望值)
     * 示例：检查 auditStatus == "APPROVED"
     */
    FIELD_EQUALS("字段等于指定值"),
    
    /**
     * 字段不为空字符串
     * 参数：fieldPath (字段路径)
     * 示例：检查 saleOrderNo 不为空字符串
     */
    FIELD_NOT_EMPTY("字段不为空字符串"),
    
    /**
     * 字段不为null
     * 参数：fieldPath (字段路径)
     * 示例：检查 orderId 不为null
     */
    FIELD_NOT_NULL("字段不为null"),
    
    /**
     * 字段值在指定集合中
     * 参数：fieldPath (字段路径), expectedValues (期望值列表)
     * 示例：检查 status 在 ["APPROVED", "COMPLETED"] 中
     */
    FIELD_IN_VALUES("字段值在指定集合中"),
    
    /**
     * 嵌套字段等于指定值
     * 参数：fieldPath (嵌套路径，如"data.validStatus"), expectedValue (期望值)
     * 示例：检查 API响应中 data.validStatus == 1
     */
    NESTED_FIELD_EQUALS("嵌套字段等于指定值"),
    
    /**
     * 数值字段大于指定值
     * 参数：fieldPath (字段路径), expectedValue (期望的最小值)
     * 示例：检查 amount > 1000
     */
    FIELD_GREATER_THAN("数值字段大于指定值"),
    
    /**
     * 销售订单完成检查
     * 专用于销售订单完成状态检查：saleOrderNo 不为null且不为空字符串
     * 参数：无需额外参数
     */
    SALES_ORDER_COMPLETE("销售订单完成检查"),
    
    /**
     * 采购订单审核通过检查
     * 专用于采购订单审核状态检查：API响应中 data.validStatus == 1
     * 参数：无需额外参数
     */
    PURCHASE_ORDER_APPROVED("采购订单审核通过检查"),
    
    /**
     * 组合条件：所有条件都必须满足 (AND逻辑)
     * 参数：conditions (子条件配置列表)
     * 示例：status=="APPROVED" AND amount>0
     */
    ALL_CONDITIONS("所有条件都必须满足"),
    
    /**
     * 组合条件：任一条件满足即可 (OR逻辑)  
     * 参数：conditions (子条件配置列表)
     * 示例：status=="APPROVED" OR status=="COMPLETED"
     */
    ANY_CONDITION("任一条件满足即可");
    
    private final String description;
    
    CheckerType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取格式化的类型描述
     */
    public String getFormattedDescription() {
        return String.format("%s (%s)", this.name(), this.description);
    }
}