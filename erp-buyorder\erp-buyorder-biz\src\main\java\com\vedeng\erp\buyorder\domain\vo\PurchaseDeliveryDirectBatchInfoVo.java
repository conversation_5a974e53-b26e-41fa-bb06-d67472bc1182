package com.vedeng.erp.buyorder.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * T_PURCHASE_DELIVERY_DIRECT_BATCH_INFO
 * <AUTHOR>
@Data
public class PurchaseDeliveryDirectBatchInfoVo implements Serializable {
    private Integer purchaseDeliveryDirectBatchInfoId;

    /**
     * 采购单ID
     */
    private Integer buyorderId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    /**
     * 详情信息
     */
    private List<PurchaseDeliveryDirectBatchDetailVo> list;

    /**
     * 同行单图片
     */
    private String[] fileUrls;
    
    // 1-同行单已全部维护  0 否
    private Integer peerStatus;

    private static final long serialVersionUID = 1L;

}
