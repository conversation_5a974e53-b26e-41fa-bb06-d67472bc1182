# 统一轮询组件 (Universal Polling Component)

## 概述

这是一个全新设计的统一轮询组件，实现了"一个方法解决所有轮询场景"的目标。组件完全符合Temporal最佳实践，使用`Workflow.sleep()`实现真正的挂起轮询，零资源占用。

## 🎯 设计目标

- **完全统一**：一个方法处理所有轮询场景（远程API + 本地数据库）
- **技术分离**：明确区分远程调用和本地查询两种技术栈
- **零重复代码**：消除原有轮询组件95%的重复逻辑
- **符合最佳实践**：基于Workflow.sleep()实现真正挂起
- **完全兼容**：支持原有所有功能

## 🏗️ 架构设计

### 核心组件结构

```
com.vedeng.temporal.polling.universal/
├── enums/
│   └── DataSourceType.java              # 数据源类型枚举
├── config/
│   ├── ApiConfig.java                    # API配置类
│   ├── DatabaseConfig.java              # 数据库配置类
│   └── PollingConfig.java               # 轮询配置类
├── checker/
│   └── CompletionChecker.java           # 条件检查器接口
├── request/
│   └── UniversalPollingRequest.java     # 统一请求类
├── result/
│   └── UniversalPollingResult.java      # 统一结果类
├── executor/
│   ├── RemoteQueryExecutor.java         # 远程查询执行器接口
│   ├── LocalQueryExecutor.java          # 本地查询执行器接口
│   └── impl/
│       ├── ApiQueryExecutor.java        # API查询实现
│       └── DatabaseQueryExecutor.java   # 数据库查询实现
├── activity/
│   ├── UniversalPollingActivity.java    # Activity接口
│   └── impl/
│       └── UniversalPollingActivityImpl.java  # Activity实现
├── workflow/
│   └── UniversalPollingWorkflow.java    # 核心轮询方法
└── example/
    └── UniversalPollingExample.java     # 使用示例
```

### 设计原则

1. **技术栈分离**：
   - **远程调用**：处理HTTP API、网络延迟、超时重试
   - **本地查询**：处理数据库连接、Mapper调用、查询优化

2. **职责明确**：
   - **Activity**：执行单次查询，快速返回（<30秒）
   - **Workflow**：控制轮询逻辑，使用Workflow.sleep()挂起

3. **接口抽象**：
   - **查询执行器**：抽象不同的查询方式
   - **条件检查器**：抽象不同的完成判断逻辑

## 🚀 快速使用

### 1. 远程API轮询（对应原基础轮询）

```java
// 在Workflow中使用
public String waitForOrderApproval(String orderId, String companyCode) {
    // 1. 构建API配置
    ApiConfig apiConfig = ApiConfig.builder()
        .apiPath("/api/v1/order/status")
        .parameters(Map.of("orderId", orderId))
        .companyCode(companyCode)
        .build();
    
    // 2. 定义完成条件
    CompletionChecker<Map<String, Object>> checker = (data, context) -> 
        "APPROVED".equals(data.get("status"));
    
    // 3. 执行统一轮询
    UniversalPollingRequest<Map<String, Object>> request = 
        UniversalPollingRequest.createApiRequest(orderId, apiConfig, checker);
    
    UniversalPollingResult<Map<String, Object>> result = 
        UniversalPollingWorkflow.universalPoll(request);
    
    return result.isSuccess() ? "SUCCESS" : "FAILED";
}
```

### 2. 本地数据库轮询（对应原自定义轮询）

```java
// 在Workflow中使用
public String waitForSalesOrderData(String businessId, String companyCode) {
    // 1. 构建数据库配置
    DatabaseConfig dbConfig = DatabaseConfig.builder()
        .queryType("TEMPORAL_FLOW_ORDER_QUERY")
        .businessId(businessId)
        .queryParameters(Map.of(
            "queryTypes", List.of("SALE_ORDER"),
            "currentCompany", companyCode
        ))
        .build();
    
    // 2. 定义完成条件
    CompletionChecker<Map<String, Object>> checker = (data, context) -> {
        String saleOrderNo = (String) data.get("saleOrderNo");
        return saleOrderNo != null && !saleOrderNo.trim().isEmpty();
    };
    
    // 3. 执行统一轮询
    UniversalPollingRequest<Map<String, Object>> request = 
        UniversalPollingRequest.createDatabaseRequest(businessId, dbConfig, checker);
    
    UniversalPollingResult<Map<String, Object>> result = 
        UniversalPollingWorkflow.universalPoll(request);
    
    return result.isSuccess() ? "SUCCESS" : "FAILED";
}
```

## 🔧 高级功能

### 1. 复杂条件组合

```java
// 组合多个条件
CompletionChecker<Map<String, Object>> complexChecker = 
    ((CompletionChecker<Map<String, Object>>) (data, context) -> 
        "APPROVED".equals(data.get("status")))
    .and((data, context) -> {
        Object amount = data.get("amount");
        return amount instanceof Number && ((Number) amount).doubleValue() > 0;
    })
    .and((data, context) -> data.get("approvalId") != null);
```

### 2. 配置说明

统一轮询组件使用`TemporalProperties`中的全局配置，不需要单独配置轮询参数：

```properties
# 轮询配置（在application.yml或Apollo中配置）
temporal.polling.initial-interval-seconds=30    # 初始间隔30秒
temporal.polling.max-interval-seconds=300       # 最大间隔5分钟
temporal.polling.backoff-coefficient=1.5        # 退避系数
temporal.polling.max-timeout-days=7             # 最大超时7天
temporal.polling.heartbeat-interval-minutes=5   # 心跳间隔5分钟
temporal.polling.max-retry-count=10             # 最大重试10次
temporal.polling.verbose-logging=false          # 详细日志
```

### 3. 带超时控制的轮询

```java
UniversalPollingResult<Map<String, Object>> result = 
    UniversalPollingWorkflow.universalPollWithTimeout(request, Duration.ofMinutes(30));
```

## 📊 功能对比

| 功能特性 | 原PollingActivity | 统一轮询组件 | 改进效果 |
|---------|------------------|-------------|----------|
| **基础轮询** | ✅ pollUntilCompleted | ✅ universalPoll | 零资源占用 |
| **自定义轮询** | ✅ pollWithCustomQuery | ✅ universalPoll | 统一方法 |
| **Temporal最佳实践** | ❌ 长Activity运行 | ✅ Workflow.sleep() | 完全符合 |
| **资源占用** | ❌ 持续占用线程 | ✅ 零资源占用 | 显著改善 |
| **代码重复** | ❌ 95%重复代码 | ✅ 零重复代码 | 完全消除 |
| **技术栈分离** | ❌ 混合在一起 | ✅ 清晰分离 | 架构清晰 |
| **条件检查** | ✅ BusinessCompletionChecker | ✅ CompletionChecker | 更灵活 |
| **配置灵活性** | ⚠️ 基础配置 | ✅ 全局统一配置 | Apollo动态配置 |

## 🎯 迁移指南

### 从基础轮询迁移

```java
// 原来的方式
PollingRequest oldRequest = PollingRequest.builder()
    .businessId(businessId)
    .apiPath("/api/v1/order/status")
    .apiParameters(parameters)
    .completionChecker(checker)
    .build();

PollingResult<Map<String, Object>> oldResult = 
    pollingActivity.pollUntilCompleted(oldRequest);

// 新的方式
ApiConfig apiConfig = ApiConfig.builder()
    .apiPath("/api/v1/order/status")
    .parameters(parameters)
    .build();

UniversalPollingRequest<Map<String, Object>> newRequest = 
    UniversalPollingRequest.createApiRequest(businessId, apiConfig, checker);

UniversalPollingResult<Map<String, Object>> newResult = 
    UniversalPollingWorkflow.universalPoll(newRequest);
```

### 从自定义轮询迁移

```java
// 原来的方式
CustomPollingRequest oldRequest = CustomPollingRequest.builder()
    .businessId(businessId)
    .queryType(PollingQueryType.TEMPORAL_FLOW_ORDER_QUERY)
    .queryParameters(queryParams)
    .completionConditions(conditions)
    .build();

PollingResult<Map<String, Object>> oldResult = 
    pollingActivity.pollWithCustomQuery(oldRequest);

// 新的方式
DatabaseConfig dbConfig = DatabaseConfig.builder()
    .queryType("TEMPORAL_FLOW_ORDER_QUERY")
    .businessId(businessId)
    .queryParameters(queryParams)
    .build();

CompletionChecker<Map<String, Object>> checker = (data, context) -> {
    // 将原来的CompletionCondition逻辑转换为函数式检查
    return checkConditions(data, conditions);
};

UniversalPollingRequest<Map<String, Object>> newRequest = 
    UniversalPollingRequest.createDatabaseRequest(businessId, dbConfig, checker);

UniversalPollingResult<Map<String, Object>> newResult = 
    UniversalPollingWorkflow.universalPoll(newRequest);
```

## 💡 最佳实践

### 1. 选择合适的数据源类型
- **远程API**：跨系统调用、外部服务状态查询
- **本地数据库**：本地数据变化、内部状态查询

### 2. 配置轮询参数
- **Apollo配置中心**：统一管理所有轮询参数，支持动态更新
- **合理的超时时间**：根据业务场景设置合适的最大等待时间
- **适当的重试次数**：避免无限轮询，设置合理的最大重试次数

### 3. 设计有效的条件检查器
- 检查关键字段不为空
- 验证业务状态值
- 组合多个条件确保准确性

### 4. 监控和调试
- 启用详细日志用于调试
- 设置合理的心跳间隔
- 在Temporal UI中监控执行状态

## 🔒 注意事项

1. **只能在Workflow中使用**：这些方法必须在Temporal Workflow上下文中调用
2. **Activity实现需要Spring注入**：确保相关的Mapper和Client已正确配置
3. **合理设置超时**：避免无限轮询导致的资源浪费
4. **类型安全**：使用泛型确保类型安全
5. **异常处理**：妥善处理查询异常和网络异常

## 🚀 版本历史

- **v1.0** (2025-01-24): 
  - 实现统一轮询架构
  - 支持远程API和本地数据库两种数据源
  - 基于Workflow.sleep()的零资源占用轮询
  - 完整的配置系统和条件检查器
  - 丰富的使用示例和文档