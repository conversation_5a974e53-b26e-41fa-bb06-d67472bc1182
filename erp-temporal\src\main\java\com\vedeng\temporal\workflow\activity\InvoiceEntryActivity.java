package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.Map;

/**
 * 发票录入Activity接口
 * 
 * 设计理念：
 * - 将InvoiceEntryFlow中的业务方法拆分为独立的Activity方法
 * - 支持细粒度重试，失败时只重试失败的步骤
 * - 保持架构简单，统一包结构便于管理
 * 
 * 核心功能：
 * - 发票录入：执行完整的发票录入流程（查询详情 + 录入 + 审核）
 * - 发票详情查询：查询发票详情，用于流程控制
 * - 发票审核：执行发票审核操作
 * 
 * 业务流程：
 * 1. 前置检查：查询采购订单状态、供应商信息、发票规则
 * 2. 数据准备：组装发票数据、计算税额、验证业务规则
 * 3. 发票录入：调用录入接口、查询录入状态、等待录入完成
 * 4. 提交审核：检查提交条件、调用提交接口、查询提交状态
 * 5. 审核处理：检查审核权限、调用审核接口、查询最终状态
 * 
 * API接口：
 * - 发票详情查询：/api/v1/receiptInvoice/queryInvoiceDetail.do
 * - 发票录入：/api/v1/receiptInvoice/create.do
 * - 发票审核：/api/v1/receiptInvoice/approve.do
 * 
 * 重试策略：
 * - 创建操作：重试3次，适合网络异常恢复
 * - 查询操作：重试5次，适合临时性查询失败
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-21
 */
@ActivityInterface
public interface InvoiceEntryActivity {
    

    
    /**
     * 审核发票
     *
     * 功能说明：
     * - 对已录入的发票执行审核操作
     * - 支持自动审核通过
     * - 支持幂等性，重复审核不会产生副作用
     *
     * 重试策略：
     * - 最大重试次数：1次
     * - 初始间隔：5秒
     * - 避免重复审核
     *
     * @param request 业务请求，包含要审核的发票信息
     * @return 审核结果，成功时发票状态变为已审核
     */
    @ActivityMethod
    CompanyBusinessResponse approveInvoice(CompanyBusinessRequest request);

    /**
     * 基于预查询的发票详情创建发票录入
     *
     * 功能说明：
     * - 使用预先查询好的发票详情数据创建发票录入
     * - 避免重复查询，提高效率
     * - 支持幂等性，重试时不会重复创建
     *
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     *
     * @param request 业务请求
     * @param invoiceDetailData 发票详情查询结果数据
     * @return 创建结果，成功时包含发票ID
     */
    @ActivityMethod
    CompanyBusinessResponse createInvoiceWithDetails(CompanyBusinessRequest request,
                                                     Map<String, Object> invoiceDetailData);
}
