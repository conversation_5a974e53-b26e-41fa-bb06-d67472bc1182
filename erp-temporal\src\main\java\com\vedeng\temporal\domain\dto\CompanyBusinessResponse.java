package com.vedeng.temporal.domain.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.Map;
import java.util.HashMap;

/**
 * 多公司业务响应对象
 * 用于返回业务处理结果
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanyBusinessResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处理是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 业务结果数据
     */
    private Object resultData;

    /**
     * 生成的业务单据ID
     */
    private String generatedDocumentId;

    /**
     * 处理时间戳
     */
    private Long processTimestamp;

    /**
     * 处理耗时（毫秒）
     */
    private Long processDuration;


    /**
     * 创建成功响应
     */
    public static CompanyBusinessResponse success(String message, String documentId) {
        return CompanyBusinessResponse.builder()
                .success(true)
                .message(message)
                .generatedDocumentId(documentId)
                .processTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建成功响应（带业务结果数据）
     */
    public static CompanyBusinessResponse success(String message, String documentId, Object resultData) {
        return CompanyBusinessResponse.builder()
                .success(true)
                .message(message)
                .generatedDocumentId(documentId)
                .resultData(resultData)
                .processTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建成功响应（仅包含结果数据）
     */
    public static CompanyBusinessResponse successWithData(String message, Object resultData) {
        return CompanyBusinessResponse.builder()
                .success(true)
                .message(message)
                .resultData(resultData)
                .processTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败响应
     */
    public static CompanyBusinessResponse failure(String message, String errorCode) {
        return CompanyBusinessResponse.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .processTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败响应（带异常）
     */
    public static CompanyBusinessResponse failure(String message, String errorCode, Exception e) {
        return CompanyBusinessResponse.builder()
                .success(false)
                .message(message + ": " + e.getMessage())
                .errorCode(errorCode)
                .processTimestamp(System.currentTimeMillis())
                .build();
    }

    // ========== 便利数据访问方法 ==========

    /**
     * 获取结果数据并转换为指定类型
     * 
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的数据，转换失败或数据为null时返回null
     */
    public <T> T getResultDataAs(Class<T> clazz) {
        if (resultData == null) {
            return null;
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.convertValue(resultData, clazz);
        } catch (Exception e) {
            // 转换失败，记录警告并返回null
            return null;
        }
    }

    /**
     * 获取结果数据作为Map对象
     * 
     * @return Map形式的结果数据，数据为null时返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getResultDataAsMap() {
        if (resultData == null) {
            return new HashMap<>();
        }
        
        if (resultData instanceof Map) {
            return (Map<String, Object>) resultData;
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.convertValue(resultData, Map.class);
        } catch (Exception e) {
            // 转换失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 检查是否有结果数据
     * 
     * @return true如果有非null的结果数据
     */
    public boolean hasResultData() {
        return resultData != null;
    }

    /**
     * 从结果数据中获取指定键的值
     * 
     * @param key 键名
     * @return 键对应的值，如果不存在或不是Map类型则返回null
     */
    public Object getResultValue(String key) {
        Map<String, Object> dataMap = getResultDataAsMap();
        return dataMap.get(key);
    }

    /**
     * 从结果数据中获取指定键的值并转换为指定类型
     * 
     * @param key 键名
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的值，如果不存在或转换失败则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getResultValue(String key, Class<T> clazz) {
        Object value = getResultValue(key);
        if (value == null) {
            return null;
        }
        
        if (clazz.isInstance(value)) {
            return (T) value;
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.convertValue(value, clazz);
        } catch (Exception e) {
            return null;
        }
    }
}
