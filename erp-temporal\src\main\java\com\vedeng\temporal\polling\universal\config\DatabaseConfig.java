package com.vedeng.temporal.polling.universal.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 数据库配置类 - 极简版
 * 
 * 专为polling组件设计的极简数据库配置，只保留核心必需参数。
 * 提供一行代码创建配置的能力，大幅简化调用方的使用复杂度。
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (极简版)
 * @since 2025-01-24
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class DatabaseConfig {
    
    /**
     * 查询类型 (必需)
     * 对应PollingQueryType枚举值名称
     * 例如："TEMPORAL_FLOW_ORDER_QUERY", "BASE_COMPANY_INFO_QUERY"
     */
    private String queryType;
    
    /**
     * 业务ID (必需)
     * 用于查询的业务标识
     */
    private String businessId;
    
    /**
     * 查询参数 (必需)
     * 传递给查询方法的参数Map
     */
    private Map<String, Object> queryParameters;
    
    /**
     * 公司代码 (必需)
     * 用于多租户场景下的数据隔离，统一公司标识
     */
    private String companyCode;
    
    /**
     * 前置公司代码 (可选)
     * 用于跨公司业务流程的查询，大部分场景为null
     */
    private String previousCompany;
    
    /**
     * 极简创建方法 - 推荐使用
     * 
     * 一行代码创建数据库配置，适用于95%的常规查询场景。
     * 
     * @param queryType 查询类型
     * @param businessId 业务ID  
     * @param queryParameters 查询参数
     * @param companyCode 公司代码
     * @return 数据库配置实例
     */
    public static DatabaseConfig create(String queryType, String businessId, 
                                      Map<String, Object> queryParameters, String companyCode) {
        return new DatabaseConfig(queryType, businessId, queryParameters, companyCode, null);
    }
    
    /**
     * 跨公司查询创建方法
     * 
     * 用于需要指定前置公司的跨公司业务流程查询。
     * 
     * @param queryType 查询类型
     * @param businessId 业务ID
     * @param queryParameters 查询参数
     * @param companyCode 当前公司代码
     * @param previousCompany 前置公司代码
     * @return 数据库配置实例
     */
    public static DatabaseConfig createCrossCompany(String queryType, String businessId, 
                                                   Map<String, Object> queryParameters,
                                                   String companyCode, String previousCompany) {
        return new DatabaseConfig(queryType, businessId, queryParameters, companyCode, previousCompany);
    }
    
    /**
     * 验证配置的有效性
     * 
     * @throws IllegalArgumentException 如果配置无效
     */
    public void validate() {
        if (queryType == null || queryType.trim().isEmpty()) {
            throw new IllegalArgumentException("查询类型不能为空");
        }
        
        if (businessId == null || businessId.trim().isEmpty()) {
            throw new IllegalArgumentException("业务ID不能为空");
        }
        
        if (companyCode == null || companyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("公司代码不能为空");
        }
    }
}