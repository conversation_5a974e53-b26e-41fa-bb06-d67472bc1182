package com.vedeng.temporal.notification;

import com.vedeng.temporal.config.TemporalProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 通知配置管理类（简化版）
 * 统一使用单个机器人key通知所有错误
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0
 * @since 2025-01-25
 */
@Component
public class NotificationConfig {

    @Autowired
    private TemporalProperties temporalProperties;

    /**
     * 是否启用通知功能
     */
    public boolean isEnabled() {
        return temporalProperties.getNotification().isEnabled();
    }

    /**
     * 是否异步发送通知
     */
    public boolean isAsync() {
        return temporalProperties.getNotification().isAsync();
    }

    /**
     * 获取统一机器人Key（用于所有错误通知）
     */
    public String getRobotKey() {
        return temporalProperties.getNotification().getRobotBusiness();
    }

    /**
     * 是否启用去重功能
     */
    public boolean isDeduplicationEnabled() {
        return temporalProperties.getNotification().isDeduplicationEnabled();
    }

    /**
     * 获取去重窗口（分钟）
     */
    public int getDeduplicationWindow() {
        return temporalProperties.getNotification().getDeduplicationWindowBusiness();
    }
}
