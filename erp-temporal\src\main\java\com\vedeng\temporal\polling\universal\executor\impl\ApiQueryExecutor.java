package com.vedeng.temporal.polling.universal.executor.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.enums.SystemSourceEnum;
import com.vedeng.temporal.polling.universal.config.ApiConfig;
import com.vedeng.temporal.polling.universal.executor.RemoteQueryExecutor;
import com.vedeng.temporal.util.SystemApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * API查询执行器实现类
 * 
 * 实现远程API查询功能，专门处理HTTP API调用相关的逻辑。
 * 集成了SystemApiClient来执行实际的API调用，并提供了完整的错误处理机制。
 * 
 * 主要功能：
 * - 执行HTTP API调用
 * - 解析JSON响应数据
 * - 处理API错误码和异常
 * - 提供健康检查功能
 * 
 * 重试机制：
 * API调用失败时依赖Temporal的Activity重试机制，不在此层面进行重试。
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@Slf4j
@Component
public class ApiQueryExecutor implements RemoteQueryExecutor<Map<String, Object>> {
    
    @Autowired
    private SystemApiClient systemApiClient;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Map<String, Object> executeRemoteQuery(Map<String, Object> context) throws Exception {
        ApiConfig apiConfig = (ApiConfig) context.get("apiConfig");
        if (apiConfig == null) {
            throw new IllegalArgumentException("API配置不能为空");
        }
        
        log.debug("执行API查询，路径: {}, 公司: {}", apiConfig.getApiPath(), apiConfig.getCompanyCode());
        
        try {
            // 验证API配置
            apiConfig.validate();
            
            // 构建请求参数
            Map<String, Object> requestData = buildRequestData(apiConfig);
            
            // 执行API调用
            String responseJson = executeApiCall(apiConfig, requestData);
            
            // 解析响应数据
            Map<String, Object> responseData = parseApiResponse(responseJson);
            
            // 验证响应格式
            validateApiResponse(responseData, apiConfig);
            
            log.debug("API查询成功，路径: {}, 响应数据: {}", apiConfig.getApiPath(), responseData);
            return responseData;
            
        } catch (Exception e) {
            log.error("API查询失败，路径: {}, 公司: {}, 错误: {}", 
                    apiConfig.getApiPath(), apiConfig.getCompanyCode(), e.getMessage());
            throw new RuntimeException("API查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建请求参数
     * 
     * @param apiConfig API配置
     * @return 请求参数Map
     */
    private Map<String, Object> buildRequestData(ApiConfig apiConfig) {
        Map<String, Object> requestData = new HashMap<>();
        
        // 添加API配置中的参数
        if (apiConfig.getParameters() != null && !apiConfig.getParameters().isEmpty()) {
            requestData.putAll(apiConfig.getParameters());
        }
        
        // 添加公司代码到请求参数（如果需要）
        if (apiConfig.getCompanyCode() != null) {
            requestData.put("companyCode", apiConfig.getCompanyCode());
        }
        
        log.debug("构建API请求参数: {}", requestData);
        return requestData;
    }
    
    /**
     * 执行API调用
     * 
     * @param apiConfig API配置
     * @param requestData 请求参数
     * @return API响应JSON字符串
     */
    private String executeApiCall(ApiConfig apiConfig, Map<String, Object> requestData) throws Exception {
        log.debug("执行API调用，路径: {}", apiConfig.getApiPath());
        
        // 执行实际的API调用
        String response = systemApiClient
                .withCompany(apiConfig.getCompanyCode())
                .postToSystemApi(apiConfig.getApiPath(), requestData, 
                        SystemSourceEnum.valueOf(apiConfig.getSystemSource()));
        
        log.debug("API调用成功");
        return response;
    }
    
    /**
     * 解析API响应数据
     * 
     * @param responseJson 响应JSON字符串
     * @return 解析后的响应数据
     */
    private Map<String, Object> parseApiResponse(String responseJson) throws Exception {
        if (responseJson == null || responseJson.trim().isEmpty()) {
            log.warn("API响应为空");
            return Collections.emptyMap();
        }
        
        try {
            Map<String, Object> responseData = objectMapper.readValue(responseJson, 
                    new TypeReference<Map<String, Object>>() {});
            
            log.debug("API响应解析成功，数据: {}", responseData);
            return responseData;
            
        } catch (Exception e) {
            log.error("API响应解析失败，原始响应: {}", responseJson, e);
            throw new RuntimeException("API响应解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证API响应格式
     * 
     * @param responseData 响应数据
     * @param apiConfig API配置
     */
    private void validateApiResponse(Map<String, Object> responseData, ApiConfig apiConfig) {
        if (responseData == null) {
            throw new RuntimeException("API响应数据为空");
        }
        
        // 检查响应码
        Integer code = (Integer) responseData.get("code");
        if (code == null) {
            log.warn("API响应中没有code字段，路径: {}", apiConfig.getApiPath());
        } else if (code != 0) {
            String message = (String) responseData.get("message");
            throw new RuntimeException(String.format("API调用失败，响应码: %d, 消息: %s", code, message));
        }
        
        // 检查数据字段
        Object data = responseData.get("data");
        if (data == null) {
            log.debug("API响应中没有data字段或data为null，路径: {}", apiConfig.getApiPath());
        }
    }
    
    
    @Override
    public String getExecutorName() {
        return "ApiQueryExecutor";
    }
}