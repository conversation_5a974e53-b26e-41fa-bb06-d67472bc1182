package com.vedeng.api.standard.validation.rules;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.api.standard.adapter.express.dto.ExpressCreateItemDto;
import com.vedeng.api.standard.adapter.express.dto.ExpressCreateRequest;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.ValidationResult;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.common.core.utils.ErpDateUtils;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.model.vo.BuyorderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AddExpressEnableRule implements ValidationRule<ExpressCreateRequest> {
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    @Autowired
    private ExpressService expressService;

    @Override
    public ValidationResult validate(ExpressCreateRequest request, Map<String, Object> context) {

        BuyOrderApiDto apiDto = buyorderApiService.getBuyorderByBuyorderNo(request.getBuyOrderNo());
        Integer buyOrderId = apiDto.getBuyorderId();
        List<BuyorderGoodsApiDto> goodsApiDtos = apiDto.getBuyorderGoodsApiDtos();
        // map ：sku - 采购商品ID
        Map<String, Integer> skuMap = goodsApiDtos.stream().collect(Collectors.toMap(BuyorderGoodsApiDto::getSku, BuyorderGoodsApiDto::getBuyorderGoodsId));
        List<ExpressCreateItemDto> goodsList = request.getItemList();
        if (CollUtil.isEmpty(goodsList)){
            return ValidationResult.failure("商品不能为空");
        }
        for (ExpressCreateItemDto expressCreateItemDto : goodsList) {
            // 找到采购商品
            Integer buyOrderGoodsId = skuMap.get(expressCreateItemDto.getSku());
            expressCreateItemDto.setBuyOrderGoodsId(buyOrderGoodsId);
            expressCreateItemDto.setBuyOrderId(buyOrderId);
        }
        ExpressCreateItemDto itemDto = goodsList.get(0);
        Express express = new Express();
        express.setLogisticsNo(itemDto.getLogisticsNo());
        Express expressInfo = expressService.getExpressInfoByNo(express);
        if (Objects.nonNull(expressInfo) && Objects.nonNull(expressInfo.getLogisticsNo())){
            return ValidationResult.failure(expressInfo.getLogisticsNo()+"快递已存在");
        }
        // 判断是否已生成快递
        String id_num_price = "";
        String id_sendN_sendedN_sumN = "";
        // 查询已发货数量
        // buyOrderGoodsId | 本次发货数 | 单价 | SKU1 _  buyOrderGoodsId | 本次发货数 | 单价 | SKU2
        // buyOrderGoodsId | 本次发货数 | 已发货数量 | 商品总数 _ buyOrderGoodsId | 本次发货数 | 已发货数量 | 商品总数
        for (ExpressCreateItemDto addExpressItemDto : goodsList) {
            id_num_price = id_num_price + addExpressItemDto.getBuyOrderGoodsId() + "|" + addExpressItemDto.getSendNum() + "|" + addExpressItemDto.getPrice() + "|" + addExpressItemDto.getSku() + "_";
            id_sendN_sendedN_sumN = id_sendN_sendedN_sumN + addExpressItemDto.getBuyOrderGoodsId() + "|" + addExpressItemDto.getSendNum() + "|" + 0 + "|" + addExpressItemDto.getNum() + "_";
        }

        Express addExpress = new Express();
        addExpress.setLogisticsNo(itemDto.getLogisticsNo());
        addExpress.setLogisticsId(itemDto.getLogisticsId());
        addExpress.setBuyorderId(buyOrderId);
        addExpress.setDeliveryTime(itemDto.getDeliveryTime());
        addExpress.setLogisticsComments(itemDto.getLogisticsComments());

        BuyorderVo buyorderVo = new BuyorderVo();
        buyorderVo.setBuyorderId(buyOrderId);
        // 将参数直接存储到验证上下文中，以便后续使用
        context.put(ValidationContextKeys.EXPRESS, addExpress);
        context.put(ValidationContextKeys.ID_NUM_PRICE, id_num_price);
        context.put(ValidationContextKeys.ID_SEND_N_SENDED_N_SUM_N, id_sendN_sendedN_sumN);
        context.put(ValidationContextKeys.BUYORDER_VO, buyorderVo);
        context.put(ValidationContextKeys.AMOUNT, itemDto.getAmount());
        // long 转 YYYY-MM-DD
        context.put(ValidationContextKeys.DELIVERY_TIMES, ErpDateUtils.convertString(itemDto.getDeliveryTime(), "yyyy-MM-dd"));
        
        
        return ValidationResult.success();
    }
    
    @Override
    public String getRuleName() {
        return "ExpressEnableRule";
    }
}
