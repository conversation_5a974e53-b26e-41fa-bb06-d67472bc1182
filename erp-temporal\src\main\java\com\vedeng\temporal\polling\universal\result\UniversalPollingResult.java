package com.vedeng.temporal.polling.universal.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 统一轮询结果类 - 极简版
 * 
 * 简化版的轮询结果类，只保留业务层真正需要的核心字段。
 * 移除了大量扩展字段，大幅简化结果处理逻辑。
 * 
 * 设计特点：
 * - 极简字段：只保留5个核心字段
 * - 类型安全：使用泛型支持不同的返回数据类型
 * - 便捷构造：提供静态工厂方法
 * - 状态清晰：明确的成功/失败判断方法
 * 
 * @param <T> 结果数据的类型
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (极简版)
 * @since 2025-01-24
 */
@Data
@Builder
@AllArgsConstructor
public class UniversalPollingResult<T> {
    
    /**
     * 是否成功完成 (必需)
     * true: 查询成功且满足完成条件
     * false: 查询失败或出现异常
     */
    private boolean success;
    
    /**
     * 结果数据 (核心)
     * 查询返回的实际数据，类型由泛型T确定
     */
    private T data;
    
    /**
     * 结果消息 (核心)
     * 成功时的描述信息或失败时的错误消息
     */
    private String message;
    
    /**
     * 尝试次数 (监控)
     * 轮询执行的总尝试次数，用于性能监控
     */
    private Integer attemptCount;
    
    /**
     * 执行耗时 (监控)
     * 从开始轮询到完成的总时间，单位毫秒
     */
    private Long durationMillis;
    
    // ==================== 便捷构造方法 ====================
    
    /**
     * 创建成功结果
     * 
     * @param data 结果数据
     * @param message 结果消息
     * @param <T> 数据类型
     * @return 成功结果实例
     */
    public static <T> UniversalPollingResult<T> success(T data, String message) {
        return new UniversalPollingResult<>(true, data, message, null, null);
    }
    
    /**
     * 创建成功结果（带执行信息）
     * 
     * @param data 结果数据
     * @param message 结果消息
     * @param attemptCount 尝试次数
     * @param durationMillis 执行耗时
     * @param <T> 数据类型
     * @return 成功结果实例
     */
    public static <T> UniversalPollingResult<T> success(T data, String message, 
                                                       Integer attemptCount, Long durationMillis) {
        return new UniversalPollingResult<>(true, data, message, attemptCount, durationMillis);
    }
    
    /**
     * 创建失败结果
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败结果实例
     */
    public static <T> UniversalPollingResult<T> failure(String message) {
        return new UniversalPollingResult<>(false, null, message, null, null);
    }
    
    /**
     * 创建失败结果（带执行信息）
     * 
     * @param message 错误消息
     * @param attemptCount 尝试次数
     * @param durationMillis 执行耗时
     * @param <T> 数据类型
     * @return 失败结果实例
     */
    public static <T> UniversalPollingResult<T> failure(String message, 
                                                       Integer attemptCount, Long durationMillis) {
        return new UniversalPollingResult<>(false, null, message, attemptCount, durationMillis);
    }
    
    /**
     * 创建超时结果
     * 
     * @param message 超时消息
     * @param attemptCount 尝试次数
     * @param durationMillis 执行耗时
     * @param <T> 数据类型
     * @return 超时结果实例
     */
    public static <T> UniversalPollingResult<T> timeout(String message, 
                                                       Integer attemptCount, Long durationMillis) {
        return new UniversalPollingResult<>(false, null, message, attemptCount, durationMillis);
    }
    
    // ==================== 状态判断方法 ====================
    
    /**
     * 是否为失败结果
     * 
     * @return true 如果失败
     */
    @JsonIgnore
    public boolean checkFailure() {
        return !success;
    }
    
    /**
     * 获取格式化的结果描述
     * 用于日志输出和调试
     * 
     * @return 格式化的结果描述
     */
    @JsonIgnore
    public String formatDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("UniversalPollingResult{");
        sb.append("success=").append(success);
        sb.append(", message='").append(message).append("'");
        if (attemptCount != null) {
            sb.append(", attemptCount=").append(attemptCount);
        }
        if (durationMillis != null) {
            sb.append(", durationMillis=").append(durationMillis);
        }
        sb.append("}");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return formatDescription();
    }
}