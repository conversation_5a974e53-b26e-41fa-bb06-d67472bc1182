package com.vedeng.temporal.exception;

import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.notification.NotificationContext;
import com.vedeng.temporal.notification.TemporalNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异常处理工具类（精简优化版）
 *
 * 提供统一的异常转换和处理方法：
 * 1. 统一的结果检查和异常转换
 * 2. 完整的业务异常处理流程
 * 3. 一行代码异常处理（handleAndThrowActivityException）
 * 4. 与 ErrorClassifier 协作，单一职责分工
 *
 * 精简说明：
 * - 合并相似的检查方法，减少重复代码
 * - 移除轮询异常的特殊处理逻辑
 * - 简化异常处理流程，统一日志记录
 * - 移除职责重叠的方法，提高代码清晰度
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0 (精简优化版)
 * @since 2025-01-25
 */
@Component
@Slf4j
public class ExceptionHandler {

    @Autowired
    private ErrorClassifier errorClassifier;

    @Autowired
    private TemporalNotificationService notificationService;
    

    /**
     * 完整的业务异常处理流程（精简版）
     * 统一处理异常分类、通知发送、响应构建
     *
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @return CompanyBusinessResponse 或抛出异常触发重试
     */
    public CompanyBusinessResponse handleBusinessException(Exception e, String operationName,
                                                         String businessId, String companyCode) {
        try {
            // 如果已经是 BusinessProcessException，直接处理
            if (e instanceof BusinessProcessException) {
                return handleBusinessProcessException((BusinessProcessException) e, operationName, businessId);
            }

            // 对于其他异常，使用ErrorClassifier进行统一分类处理
            BusinessProcessException businessException = errorClassifier.classifyException(
                e, null, "ACTIVITY", operationName);

            return handleBusinessProcessException(businessException, operationName, businessId);

        } catch (Exception handlingException) {
            // 异常处理过程中出现异常，使用兜底策略
            log.error("异常处理过程中发生错误，操作: {}, 业务ID: {}", operationName, businessId, handlingException);
            return CompanyBusinessResponse.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    /**
     * 处理Activity异常并直接抛出（精简版）
     * 
     * 专为Activity层设计的一行代码异常处理解决方案
     * 
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @throws BusinessProcessException 业务异常时抛出
     * @throws RuntimeException 技术异常时抛出（触发Temporal重试）
     */
    public void handleAndThrowActivityException(Exception e, String operationName, 
                                              String businessId, String companyCode) {
        // 复用现有的handleBusinessException逻辑，获得完整的异常处理能力
        CompanyBusinessResponse response = handleBusinessException(e, operationName, businessId, companyCode);
        
        // 如果返回了失败响应（业务异常），转换为BusinessProcessException抛出
        // 技术异常已经在handleBusinessException中抛出RuntimeException，不会执行到这里
        if (!response.getSuccess()) {
            throw BusinessProcessException.fromActivity(
                response.getMessage(), 
                response.getErrorCode(), 
                false,  // 业务异常不重试
                operationName);
        }
    }

    // ========== 私有辅助方法 ==========
    
    /**
     * 处理 BusinessProcessException（简化版）
     * 统一处理所有业务异常，不再区分轮询异常
     */
    private CompanyBusinessResponse handleBusinessProcessException(BusinessProcessException e,
                                                                 String operationName, String businessId) {
        // 创建通知上下文
        NotificationContext context = NotificationContext.builder()
                .operationName(operationName)
                .businessId(businessId)
                .targetCompany(e.getCompanyCode())
                .build();

        if (e.isRetryable()) {
            // 技术异常：发送通知并让 Temporal 重试
            logException(operationName, e.getCompanyCode(), e.getErrorCode(), e.getMessage(), true, businessId);

            context.withException(e, 0L);
            notificationService.sendTechnicalErrorNotification(context);

            // 抛出 RuntimeException 触发 Temporal 重试
            throw new RuntimeException(e.getMessage(), e);

        } else {
            // 业务异常：发送通知但不重试
            logException(operationName, e.getCompanyCode(), e.getErrorCode(), e.getMessage(), false, businessId);

            context.withException(e, 0L);
            notificationService.sendBusinessFailureNotification(context);

            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        }
    }

    /**
     * 统一的日志记录方法
     */
    private void logException(String operationName, String companyCode, String errorCode, 
                            String message, boolean retryable, String businessId) {
        if (retryable) {
            if (businessId != null) {
                log.warn("技术异常，将重试: 操作={}, 公司={}, 业务ID={}, 错误码={}, 消息={}", 
                        operationName, companyCode, businessId, errorCode, message);
            } else {
                log.warn("技术异常，将重试: 操作={}, 公司={}, 错误码={}, 消息={}", 
                        operationName, companyCode, errorCode, message);
            }
        } else {
            if (businessId != null) {
                log.error("业务异常，不重试: 操作={}, 公司={}, 业务ID={}, 错误码={}, 消息={}", 
                        operationName, companyCode, businessId, errorCode, message);
            } else {
                log.error("业务异常，不重试: 操作={}, 公司={}, 错误码={}, 消息={}", 
                        operationName, companyCode, errorCode, message);
            }
        }
    }
}