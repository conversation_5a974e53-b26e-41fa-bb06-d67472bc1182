package com.vedeng.temporal.workflow.process;


import com.vedeng.temporal.context.CompanyContextCalculator;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.workflow.activity.FlowOrderInfoActivity;
import com.vedeng.temporal.workflow.activity.NotificationActivity;
import com.vedeng.temporal.workflow.activity.PollingActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.exception.ErrorClassifier;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 业务流程抽象基类
 * 提供统一的执行框架
 *
 * <AUTHOR>
 * @version 2.0 (简化版)
 * @since 2025-01-03
 */
@Slf4j
public abstract class AbstractBusinessProcess {

    // Activity 依赖（通过构造函数注入）
    protected final NotificationActivity notificationActivity;
    protected final FlowOrderInfoActivity flowOrderInfoActivity;
    protected final CompanySequenceActivity companySequenceActivity;
    protected final PollingActivity pollingActivity;

    // 异常处理分类器（统一异常处理）
    protected final ErrorClassifier errorClassifier;

    // 上下文计算器（复用实例）
    private final CompanyContextCalculator contextCalculator;


    /**
     * 构造函数
     *
     * @param notificationActivity    通知Activity（可选）
     * @param flowOrderInfoActivity   汇总信息Activity（可选）
     * @param companySequenceActivity 公司序列Activity（必需）
     * @param pollingActivity         轮询Activity（必需）
     * @param errorClassifier         异常分类器（必需，统一异常处理）
     */
    protected AbstractBusinessProcess(
            NotificationActivity notificationActivity,
            FlowOrderInfoActivity flowOrderInfoActivity,
            CompanySequenceActivity companySequenceActivity,
            PollingActivity pollingActivity,
            ErrorClassifier errorClassifier) {

        // 必需依赖检查
        this.companySequenceActivity = Objects.requireNonNull(companySequenceActivity, "companySequenceActivity不能为空");
        this.pollingActivity = Objects.requireNonNull(pollingActivity, "pollingActivity不能为空");
        this.errorClassifier = Objects.requireNonNull(errorClassifier, "errorClassifier不能为空");

        // 可选依赖
        this.notificationActivity = notificationActivity;
        this.flowOrderInfoActivity = flowOrderInfoActivity;

        // 初始化复用对象
        this.contextCalculator = new CompanyContextCalculator();
    }


    /**
     * 执行业务流程的模板方法
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        long startTime = System.currentTimeMillis();

        try {

            log.info("开始执行{}，业务ID: {}, 公司序列: {}",
                    getProcessName(), request.getBusinessId(), companySequence);


            // 1. 获取执行配置和业务步骤
            ExecutionMode executionMode = getExecutionMode();
            boolean reverseOrder = isReverseOrder();
            List<BusinessStep> steps = getBusinessSteps();

            // 2. 执行业务流程
            List<String> executionSequence = prepareExecutionSequence(companySequence, reverseOrder);
            CompanyBusinessResponse response = executeBusinessProcess(request, companySequence,
                    executionSequence, executionMode, reverseOrder, steps);

            long duration = System.currentTimeMillis() - startTime;
            log.info("{}执行完成，业务ID: {}, 耗时: {}ms",
                    getProcessName(), request.getBusinessId(), duration);


            return response;

        } catch (BusinessProcessException e) {
            // 业务异常：转换为失败响应，保持 Temporal 工作流状态为 Completed
            // 这样避免触发 Temporal 自动重试，由上层工作流进行精细的错误处理
            long duration = System.currentTimeMillis() - startTime;
            log.error("{}执行失败，业务ID: {}, 耗时: {}ms", getProcessName(), request.getBusinessId(), duration, e);

            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());

        } catch (Exception e) {
            // 未知异常：包装为业务异常后转换为失败响应
            // 统一异常处理策略：所有异常最终都转换为 CompanyBusinessResponse.failure()
            // 避免 Temporal 工作流进入 Failed 状态，保持业务逻辑的可控性
            long duration = System.currentTimeMillis() - startTime;
            BusinessProcessException businessException = BusinessProcessException.fromProcess(
                    String.format("%s执行失败: %s", getProcessName(), e.getMessage()),
                    "EXECUTION_FAILED", true, getProcessName()
            );

            log.error("{}执行异常，业务ID: {}, 耗时: {}ms", getProcessName(), request.getBusinessId(), duration, businessException);

            return CompanyBusinessResponse.failure(businessException.getMessage(), businessException.getErrorCode());
        }
    }


    // ==================== 抽象方法 ====================

    /**
     * 获取执行模式
     */
    protected abstract ExecutionMode getExecutionMode();

    /**
     * 是否逆序执行
     */
    protected abstract boolean isReverseOrder();

    /**
     * 获取业务步骤列表
     */
    protected abstract List<BusinessStep> getBusinessSteps();

    /**
     * 获取流程名称
     */
    protected abstract String getProcessName();


    // ==================== 核心执行方法 ====================

    /**
     * 准备执行序列
     */
    private List<String> prepareExecutionSequence(List<String> originalSequence, boolean reverseOrder) {
        return contextCalculator.prepareExecutionSequence(originalSequence, reverseOrder);
    }

    /**
     * 执行具体的业务流程
     */
    private CompanyBusinessResponse executeBusinessProcess(CompanyBusinessRequest request,
                                                           List<String> originalSequence,
                                                           List<String> executionSequence,
                                                           ExecutionMode executionMode,
                                                           boolean reverseOrder,
                                                           List<BusinessStep> steps) {

        log.info("开始执行{}，公司序列: {}, 执行模式: {}",
                getProcessName(), executionSequence, executionMode);

        // 根据执行模式选择不同的处理方式
        switch (executionMode) {
            case SEQUENTIAL:
                return executeSequentially(request, originalSequence, executionSequence, reverseOrder, steps);
            case PARALLEL:
                return executeInParallel(request, originalSequence, executionSequence, reverseOrder, steps);
            default:
                return executeSequentially(request, originalSequence, executionSequence, reverseOrder, steps);
        }
    }

    /**
     * 串行执行模式
     * 公司按顺序依次执行，后一个公司等待前一个公司完成
     */
    private CompanyBusinessResponse executeSequentially(CompanyBusinessRequest request,
                                                        List<String> originalSequence,
                                                        List<String> executionSequence,
                                                        boolean reverseOrder,
                                                        List<BusinessStep> steps) {

        log.info("使用串行执行模式，公司序列: {}", executionSequence);

        for (int i = 0; i < executionSequence.size(); i++) {
            String currentCompany = executionSequence.get(i);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, originalSequence, executionSequence, i, reverseOrder, ExecutionMode.SEQUENTIAL);

            log.info("处理公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}步骤执行完成", currentCompany);
        }

        return CompanyBusinessResponse.success(getProcessName() + "串行执行完成", request.getBusinessId());
    }

    /**
     * 并行执行模式
     * 所有公司同时启动，独立执行，无相互依赖
     */
    private CompanyBusinessResponse executeInParallel(CompanyBusinessRequest request,
                                                      List<String> originalSequence,
                                                      List<String> executionSequence,
                                                      boolean reverseOrder,
                                                      List<BusinessStep> steps) {

        log.info("使用并行执行模式，公司序列: {}", executionSequence);

        // 1. 创建每个公司的执行任务
        List<Promise<CompanyBusinessResponse>> promises = new ArrayList<>();

        for (int i = 0; i < executionSequence.size(); i++) {
            final String currentCompany = executionSequence.get(i);
            final int currentIndex = i;

            // 使用 Temporal 的 Async.function 创建异步任务
            Promise<CompanyBusinessResponse> promise = Async.function(
                    () -> executeCompanyInParallel(
                            request, originalSequence, executionSequence, reverseOrder, steps,
                            currentCompany, currentIndex
                    )
            );

            promises.add(promise);
        }

        // 2. 等待所有公司执行完成
        Promise<Void> allPromises = Promise.allOf(promises);
        allPromises.get(); // 等待所有任务完成

        // 3. 获取所有任务的结果
        List<CompanyBusinessResponse> results = new ArrayList<>();
        for (Promise<CompanyBusinessResponse> promise : promises) {
            results.add(promise.get());
        }

        // 4. 检查结果
        for (CompanyBusinessResponse result : results) {
            if (!Boolean.TRUE.equals(result.getSuccess())) {
                log.error("并行执行失败，错误: {}", result.getMessage());
                return result;
            }
        }

        log.info("所有公司并行执行完成");
        return CompanyBusinessResponse.success(getProcessName() + "并行执行完成", request.getBusinessId());
    }

    /**
     * 并行模式下执行单个公司的业务步骤
     */
    private CompanyBusinessResponse executeCompanyInParallel(
            CompanyBusinessRequest request,
            List<String> originalSequence,
            List<String> executionSequence,
            boolean reverseOrder,
            List<BusinessStep> steps,
            String currentCompany,
            int currentIndex) {

        try {
            log.info("开始并行处理公司: {}", currentCompany);

            // 计算公司执行上下文
            CompanyExecutionContext context = contextCalculator.calculateSingleContext(
                    currentCompany, originalSequence, executionSequence, currentIndex, reverseOrder, ExecutionMode.PARALLEL);

            log.info("公司: {}, 上下文: {}", currentCompany, context.getContextDescription());

            // 执行业务步骤（依赖等待由 Step 内部处理）
            executeBusinessSteps(request, context, steps, currentCompany);

            log.info("公司{}并行处理完成", currentCompany);
            return CompanyBusinessResponse.success("公司" + currentCompany + "并行处理完成", request.getBusinessId());

        } catch (BusinessProcessException e) {
            log.error("公司{}并行处理业务异常", currentCompany, e);
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        } catch (Exception e) {
            log.error("公司{}并行处理异常", currentCompany, e);
            BusinessProcessException businessException = BusinessProcessException.fromProcess(
                    "公司" + currentCompany + "并行处理异常: " + e.getMessage(),
                    "PARALLEL_EXECUTION_ERROR", true, getProcessName(), currentCompany);

            return CompanyBusinessResponse.failure(businessException.getMessage(), businessException.getErrorCode());
        }
    }


    /**
     * 执行业务步骤（简化版，使用统一模板）
     */
    private void executeBusinessSteps(CompanyBusinessRequest request, CompanyExecutionContext context,
                                      List<BusinessStep> steps, String currentCompany) {

        // 1. 获取并设置流程节点ID
        try {
            Long flowNodeId = companySequenceActivity.getFlowNodeId(request.getBusinessId(), currentCompany);
            if (flowNodeId != null) {
                request.setFlowNodeId(flowNodeId);
                log.debug("设置流程节点ID，公司: {}, flowNodeId: {}", currentCompany, flowNodeId);
            }
        } catch (Exception e) {
            log.error("获取公司{}的flowNodeId失败，业务ID: {}, 错误: {}", currentCompany, request.getBusinessId(), e.getMessage(), e);
            throw BusinessProcessException.fromProcess(
                    String.format("获取公司%s的flowNodeId失败: %s", currentCompany, e.getMessage()),
                    "FLOW_NODE_ID_ERROR", true, getProcessName(), currentCompany);
        }

        // 2. 使用统一模板执行业务步骤
        for (BusinessStep step : steps) {
            // 统一的Step执行：异常处理、日志记录、监控指标都由本类处理
            CompanyBusinessResponse stepResult = executeStepWithEnhancedExceptionHandling(step, request, context);
            
            // 检查步骤执行结果
            if (!Boolean.TRUE.equals(stepResult.getSuccess())) {
                // Step模板已经处理了异常，这里转换为Process层异常
                throw BusinessProcessException.fromProcess(
                    String.format("公司 %s 执行步骤 %s 失败: %s", currentCompany, step.getStepName(), stepResult.getMessage()),
                    stepResult.getErrorCode() != null ? stepResult.getErrorCode() : "STEP_EXECUTION_FAILED",
                    true, // 步骤失败后通常可以重试整个流程
                    getProcessName(),
                    currentCompany
                );
            }
            
            log.info("公司 {} 完成步骤: {}", currentCompany, step.getStepName());

            // 更新汇总信息（异步，不影响主流程）
            updateFlowOrderInfoSafely(request, step, currentCompany);
        }
    }

    // ==================== 汇总信息更新方法 ====================

    /**
     * 安全地更新汇总信息
     * 异步执行，不影响主业务流程
     */
    private void updateFlowOrderInfoSafely(CompanyBusinessRequest request, BusinessStep step, String currentCompany) {
        if (flowOrderInfoActivity == null) {
            log.debug("未配置汇总信息Activity，跳过汇总信息更新");
            return;
        }

        try {
            // 异步更新汇总信息，不阻塞主流程
            Async.procedure(() -> updateFlowOrderInfo(request, step, currentCompany));
        } catch (Exception e) {
            log.warn("启动汇总信息更新异步任务失败，业务ID: {}, 步骤: {}, 公司: {}, 错误: {}",
                    request.getBusinessId(), step.getStepName(), currentCompany, e.getMessage());
        }
    }

    // ==================== 汇总信息更新方法 ====================

    /**
     * 异步更新汇总信息
     * <p>
     * 设计特点：
     * - 非侵入性：不影响原有业务逻辑
     * - 容错性：更新失败不影响主流程
     * - 异步处理：不阻塞业务步骤执行
     *
     * @param request        业务请求
     * @param step           业务步骤
     * @param currentCompany 当前公司
     */
    private void updateFlowOrderInfo(CompanyBusinessRequest request, BusinessStep step, String currentCompany) {
        try {
            // 检查是否配置了汇总信息Activity
            if (flowOrderInfoActivity == null) {
                log.debug("未配置汇总信息Activity，跳过汇总信息更新");
                return;
            }

            // 构建更新请求
            FlowOrderInfoUpdateRequest updateRequest = buildFlowOrderInfoUpdateRequest(request, step, currentCompany);
            if (updateRequest == null) {
                log.debug("无需更新汇总信息，业务ID: {}, 步骤: {}", request.getBusinessId(), step.getStepName());
                return;
            }

            // 异步调用汇总信息更新
            flowOrderInfoActivity.updateFlowOrderInfo(updateRequest);

            log.debug("汇总信息更新请求已发送，业务ID: {}, 步骤: {}, 节点ID: {}",
                    request.getBusinessId(), step.getStepName(), updateRequest.getFlowNodeId());

        } catch (Exception e) {
            log.warn("汇总信息更新失败，业务ID: {}, 步骤: {}, 公司: {}, 错误: {}",
                    request.getBusinessId(), step.getStepName(), currentCompany, e.getMessage());
            // 可选：发送到补偿队列进行后续处理
        }
    }

    /**
     * 构建汇总信息更新请求
     * <p>
     * 子类可以重写此方法来定制汇总信息更新逻辑
     *
     * @param request        业务请求
     * @param step           业务步骤
     * @param currentCompany 当前公司
     * @return 汇总信息更新请求，返回null表示无需更新
     */
    protected FlowOrderInfoUpdateRequest buildFlowOrderInfoUpdateRequest(CompanyBusinessRequest request,
                                                                         BusinessStep step,
                                                                         String currentCompany) {
        // 默认实现：子类可以重写此方法来提供具体的更新逻辑
        // 这里提供一个基础的实现框架

        try {
            // 直接使用 request 中已设置的 flowNodeId
            Long flowNodeId = request.getFlowNodeId();
            if (flowNodeId == null) {
                log.warn("flowNodeId 未设置，无法构建汇总信息更新请求，业务ID: {}, 公司: {}",
                        request.getBusinessId(), currentCompany);
                return null;
            }

            // 构建基础更新请求
            FlowOrderInfoUpdateRequest updateRequest = FlowOrderInfoUpdateRequest.builder()
                    .flowNodeId(flowNodeId)
                    .build();

            // 委托给步骤自己来更新状态
            step.updateFlowOrderInfo(updateRequest, request);

            return updateRequest;

        } catch (Exception e) {
            log.warn("构建汇总信息更新请求失败，业务ID: {}, 步骤: {}, 错误: {}",
                    request.getBusinessId(), step.getStepName(), e.getMessage());
            return null;
        }
    }

    // ==================== 统一异常处理方法（迁移自 UniversalStepTemplate） ====================

    /**
     * 统一的Step执行入口（增强异常处理）
     * 提供异常处理、日志记录、监控指标收集
     * 
     * @param step Step实现
     * @param request 业务请求
     * @param context 执行上下文
     * @return 执行结果
     */
    private CompanyBusinessResponse executeStepWithEnhancedExceptionHandling(BusinessStep step, CompanyBusinessRequest request, 
                                                                           CompanyExecutionContext context) {
        long startTime = System.currentTimeMillis();
        String stepName = step.getStepName();
        String companyCode = context.getCurrentCompany();
        String businessId = request.getBusinessId();

        try {
            log.info("开始执行步骤: {}, 业务ID: {}, 公司: {}", stepName, businessId, companyCode);

            // 1. 构建执行上下文数据
            Map<String, Object> contextData = buildStepContextData(request, context, step);

            // 2. 执行核心业务逻辑
            Object result = executeStepCore(step, request, context);

            // 3. 处理执行结果
            CompanyBusinessResponse response = processStepResult(result, step, request, contextData);

            long duration = System.currentTimeMillis() - startTime;
            log.info("步骤执行成功: {}, 业务ID: {}, 公司: {}, 耗时: {}ms", 
                    stepName, businessId, companyCode, duration);

            return response;

        } catch (BusinessProcessException e) {
            // 业务流程异常：增强异常信息后转换为Response
            long duration = System.currentTimeMillis() - startTime;
            BusinessProcessException enhancedException = enhanceBusinessException(e, step, request, context);
            
            logBusinessException(enhancedException, businessId, companyCode, stepName, duration);
            return convertExceptionToResponse(enhancedException);

        } catch (Exception e) {
            // 系统异常：分类后包装为业务异常
            long duration = System.currentTimeMillis() - startTime;
            BusinessProcessException classifiedException = classifyAndWrapException(e, step, request, context);
            
            logSystemException(classifiedException, businessId, companyCode, stepName, duration, e);
            return convertExceptionToResponse(classifiedException);
        }
    }

    /**
     * 执行Step的核心业务逻辑
     * 支持两种模式：异常模式和Response模式
     */
    private Object executeStepCore(BusinessStep step, CompanyBusinessRequest request, 
                                 CompanyExecutionContext context) throws Exception {
        // 兼容原有接口：调用原方法并检查返回值
        return step.execute(request, context);
    }

    /**
     * 处理Step执行结果
     */
    private CompanyBusinessResponse processStepResult(Object result, BusinessStep step, 
                                                    CompanyBusinessRequest request, 
                                                    Map<String, Object> contextData) throws BusinessProcessException {
        if (result instanceof CompanyBusinessResponse) {
            CompanyBusinessResponse response = (CompanyBusinessResponse) result;
            // 检查Response的成功状态
            if (!Boolean.TRUE.equals(response.getSuccess())) {
                // Response失败：转换为异常
                throw BusinessProcessException.fromStep(
                    response.getMessage(),
                    response.getErrorCode() != null ? response.getErrorCode() : "STEP_EXECUTION_FAILED",
                    shouldRetryBasedOnErrorCode(response.getErrorCode()),
                    step.getStepName(),
                    request.getTargetCompanyCode(),
                    request.getBusinessId(),
                    extractOperationPhase(step, contextData),
                    contextData
                );
            }
            return response;
        } else {
            // 非Response结果：构建成功Response
            return CompanyBusinessResponse.success(
                step.getStepName() + "执行成功", 
                result != null ? result.toString() : null
            );
        }
    }

    /**
     * 增强业务异常信息
     */
    private BusinessProcessException enhanceBusinessException(BusinessProcessException originalException, 
                                                           BusinessStep step, CompanyBusinessRequest request, 
                                                           CompanyExecutionContext context) {
        // 如果异常已包含完整信息，直接返回
        if (originalException.getOperationPhase() != null && originalException.getContextData() != null) {
            return originalException;
        }

        // 构建增强的上下文数据
        Map<String, Object> contextData = buildStepContextData(request, context, step);
        if (originalException.getContextData() != null) {
            contextData.putAll(originalException.getContextData());
        }

        // 返回增强的异常
        return BusinessProcessException.fromStep(
            originalException.getMessage(),
            originalException.getErrorCode(),
            originalException.isRetryable(),
            step.getStepName(),
            context.getCurrentCompany(),
            request.getBusinessId(),
            extractOperationPhase(step, contextData),
            contextData
        );
    }

    /**
     * 分类并包装系统异常
     */
    private BusinessProcessException classifyAndWrapException(Exception originalException, BusinessStep step, 
                                                           CompanyBusinessRequest request, 
                                                           CompanyExecutionContext context) {
        Map<String, Object> contextData = buildStepContextData(request, context, step);
        String operationPhase = extractOperationPhase(step, contextData);

        // 使用ErrorClassifier进行智能分类
        ErrorClassifier.ExceptionClassification classification = 
            errorClassifier.classifyException(originalException, null, operationPhase);

        return BusinessProcessException.fromStep(
            step.getStepName() + "执行异常: " + originalException.getMessage(),
            classification.getErrorCode(),
            classification.isRetryable(),
            step.getStepName(),
            context.getCurrentCompany(),
            request.getBusinessId(),
            operationPhase,
            contextData
        );
    }

    /**
     * 构建执行上下文数据
     */
    private Map<String, Object> buildStepContextData(CompanyBusinessRequest request, 
                                                   CompanyExecutionContext context, BusinessStep step) {
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("stepType", step.getStepType().name());
        contextData.put("stepName", step.getStepName());
        contextData.put("currentCompany", context.getCurrentCompany());
        contextData.put("businessId", request.getBusinessId());
        contextData.put("flowNodeId", request.getFlowNodeId());

        if (context.getPreviousCompany() != null) {
            contextData.put("previousCompany", context.getPreviousCompany());
        }
        if (context.getNextCompany() != null) {
            contextData.put("nextCompany", context.getNextCompany());
        }
        
        return contextData;
    }

    /**
     * 提取操作阶段信息
     */
    private String extractOperationPhase(BusinessStep step, Map<String, Object> contextData) {
        // 基于Step类型和名称推断操作阶段
        String stepName = step.getStepName().toUpperCase();
        
        if (stepName.contains("创建") || stepName.contains("CREATE")) {
            return "CREATE_ORDER";
        } else if (stepName.contains("提交") || stepName.contains("SUBMIT")) {
            return "SUBMIT_APPROVAL";
        } else if (stepName.contains("审核") || stepName.contains("APPROVE")) {
            return "EXECUTE_APPROVAL";
        } else if (stepName.contains("查询") || stepName.contains("QUERY")) {
            return "QUERY_STATUS";
        } else if (stepName.contains("等待") || stepName.contains("WAIT")) {
            return "WAIT_PREREQUISITE";
        } else {
            return "EXECUTE_BUSINESS";
        }
    }

    /**
     * 基于错误码判断是否应该重试
     */
    private boolean shouldRetryBasedOnErrorCode(String errorCode) {
        return errorClassifier.isRetryableError(errorCode);
    }

    /**
     * 记录业务异常详细信息
     */
    private void logBusinessException(BusinessProcessException e, String businessId, String companyCode, 
                                    String stepName, long duration) {
        log.error("步骤业务异常 - 步骤: {}, 业务ID: {}, 公司: {}, 错误码: {}, 可重试: {}, 操作阶段: {}, 耗时: {}ms, 消息: {}",
                stepName, businessId, companyCode, e.getErrorCode(), e.isRetryable(), 
                e.getOperationPhase(), duration, e.getMessage(), e);
    }

    /**
     * 记录系统异常详细信息
     */
    private void logSystemException(BusinessProcessException wrappedException, String businessId, String companyCode, 
                                  String stepName, long duration, Exception originalException) {
        log.error("步骤系统异常 - 步骤: {}, 业务ID: {}, 公司: {}, 错误码: {}, 可重试: {}, 操作阶段: {}, 耗时: {}ms, 原始异常: {}",
                stepName, businessId, companyCode, wrappedException.getErrorCode(), wrappedException.isRetryable(),
                wrappedException.getOperationPhase(), duration, originalException.getClass().getSimpleName(), wrappedException);
    }

    /**
     * 将异常转换为Response
     */
    private CompanyBusinessResponse convertExceptionToResponse(BusinessProcessException e) {
        return CompanyBusinessResponse.builder()
                .success(false)
                .message(e.getMessage())
                .errorCode(e.getErrorCode())
                .processTimestamp(System.currentTimeMillis())
                .build();
    }
}
