package com.vedeng.temporal.workflow.step.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.workflow.activity.PurchaseOrderActivity;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.workflow.step.BusinessStep;
import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购订单业务步骤 - 新架构版本
 * <p>
 * 架构优化说明：
 * - 使用新的 PurchaseOrderActivity 替代 CompanyBusinessActivity
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：创建 → 提交 → 审核 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * <p>
 * 业务流程：
 * 1. waitForSalesOrderCompletion - 等待前置销售订单完成
 * 2. createPurchaseOrder - 创建采购订单，获取订单ID
 * 3. updatePurchaseOrder - 修改采购订单，传递订单ID
 * 4. submitPurchaseOrderForApproval - 提交审核，传递订单ID
 * 5. approvePurchaseOrder - 审核通过，传递订单ID
 * 6. waitForCompletion - 等待订单完成（可选）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 4.0 (新架构版本，参考 SalesOrderStep)
 * @since 2025-01-21
 */
@Slf4j
public class PurchaseOrderStep implements BusinessStep {

    private final PurchaseOrderActivity purchaseOrderActivity;

    public PurchaseOrderStep(PurchaseOrderActivity purchaseOrderActivity) {
        this.purchaseOrderActivity = purchaseOrderActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        String currentCompany = context.getCurrentCompany();
        String nextCompany = context.getNextCompany();

        log.info("开始执行采购订单步骤，业务ID: {}, 当前公司: {}, 下游公司: {}",
                request.getBusinessId(), currentCompany, nextCompany);

        try {
            // 1. 等待前置下游销售订单完成
            if (nextCompany != null) {
                log.info("等待下游公司销售订单完成，下游公司: {}, 当前公司: {}", nextCompany, currentCompany);
                String saleOrderNo = waitForSalesOrderCompletion(request, nextCompany);
                log.info("下游公司销售订单已完成，下游公司: {}, 当前公司: {}, 销售单号: {}", nextCompany, currentCompany, saleOrderNo);
            }

            // 2. 创建采购订单（Activity现在会抛出异常）
            CompanyBusinessRequest createRequest = request.withCompany(nextCompany);
            CompanyBusinessResponse createResult = purchaseOrderActivity.createPurchaseOrder(createRequest);

            String orderId = createResult.getGeneratedDocumentId();
            // 验证订单ID的有效性
            if (orderId == null || orderId.trim().isEmpty()) {
                String errorMsg = "创建采购订单失败：未获取到有效的订单ID";
                log.error("{}, 业务ID: {}", errorMsg, request.getBusinessId());
                return CompanyBusinessResponse.failure(errorMsg, "ORDER_ID_INVALID");
            }

            log.info("采购订单创建成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 3. 修改采购订单（传递订单ID）
            CompanyBusinessRequest updateRequest = buildRequestWithOrderId(createRequest, orderId);
            purchaseOrderActivity.updatePurchaseOrder(updateRequest);
            log.info("采购订单修改成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 4. 提交审核（传递订单ID）
            CompanyBusinessRequest submitRequest = buildRequestWithOrderId(createRequest, orderId);
            purchaseOrderActivity.submitPurchaseOrderForApproval(submitRequest);
            log.info("采购订单提交审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 5. 审核通过（传递订单ID）
            CompanyBusinessRequest approveRequest = buildRequestWithOrderId(createRequest, orderId);
            purchaseOrderActivity.approvePurchaseOrder(approveRequest);
            log.info("采购订单审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 6. 等待订单完成（可选，根据业务需要）
            waitForOrderCompletion(request, orderId);

            // 7. 返回成功结果，包含订单ID
            log.info("采购订单步骤执行完成，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);
            return CompanyBusinessResponse.success("采购订单流程执行成功", orderId);

        } catch (BusinessProcessException e) {
            // 业务流程异常：记录详细信息并转换为Response
            log.error("采购订单业务流程异常 - 业务ID: {}, 错误码: {}, 可重试: {}, 层次: {}, 步骤: {}, 公司: {}, 消息: {}",
                    request.getBusinessId(), e.getErrorCode(), e.isRetryable(),
                    e.getLayerName(), e.getStepName(), e.getCompanyCode(), e.getMessage(), e);

            return CompanyBusinessResponse.builder()
                    .success(false)
                    .message(e.getMessage())
                    .errorCode(e.getErrorCode())
                    .processTimestamp(System.currentTimeMillis())
                    .build();

        } catch (Exception e) {
            // 系统异常：记录并返回通用错误
            log.error("采购订单步骤系统异常，业务ID: {}", request.getBusinessId(), e);
            return CompanyBusinessResponse.failure("采购订单流程执行失败: " + e.getMessage(), "STEP_EXECUTION_ERROR");
        }
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.PURCHASE_ORDER;
    }

    @Override
    public String getStepName() {
        return "采购订单步骤";
    }

    @Override
    public String getStepDescription() {
        return "执行采购订单完整流程：等待销售订单完成 → 创建订单 → 修改订单 → 提交审核 → 审核通过";
    }


    @Override
    public void updateFlowOrderInfo(FlowOrderInfoUpdateRequest updateRequest,
                                    CompanyBusinessRequest request) {
        // 采购单创建步骤的状态更新逻辑
        updateRequest.setFlowOrderInfoType(0); // 采购类型
        updateRequest.setFlowOrderInfoNo(extractPurchaseOrderNo(request));
        updateRequest.setOrderStatus(1); // 部分审核

        log.debug("采购订单步骤更新汇总信息，业务编号: {}, flowNodeId: {}",
                updateRequest.getFlowOrderInfoNo(), updateRequest.getFlowNodeId());
    }

    /**
     * 提取采购单号
     */
    private String extractPurchaseOrderNo(CompanyBusinessRequest request) {
        // 使用 flowNodeId 构建采购单号
        Long flowNodeId = request.getFlowNodeId();
        String purchaseOrderNo = "PO" + flowNodeId + "_" + System.currentTimeMillis();
        log.debug("提取采购单号: {}", purchaseOrderNo);
        return purchaseOrderNo;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 构建包含订单ID的请求对象
     * 用于在步骤间传递订单ID数据
     */
    private CompanyBusinessRequest buildRequestWithOrderId(CompanyBusinessRequest originalRequest, String orderId) {
        // 构建包含订单ID的业务数据
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("orderId", orderId);

        // 如果原请求有业务数据，先解析并合并
        if (StringUtils.hasText(originalRequest.getBusinessData())) {
            Map<String, Object> existingData = parseBusinessData(originalRequest.getBusinessData());
            if (existingData != null) {
                businessData.putAll(existingData);
            }
        }
        businessData.put("orderId", orderId); // 确保订单ID优先级最高

        // 构建新的请求对象
        return originalRequest.toBuilder()
                .businessData(JSONUtil.toJsonStr(businessData))
                .build();
    }

    /**
     * 解析业务数据JSON字符串
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (StrUtil.isBlank(businessDataJson)) {
            return null;
        }

        try {
            return JSONUtil.toBean(businessDataJson, Map.class);
        } catch (Exception e) {
            log.warn("解析业务数据失败，使用空数据: {}", businessDataJson, e);
            return null;
        }
    }


    /**
     * 等待前置销售订单完成并返回销售单号
     * 使用统一轮询组件进行数据库查询轮询
     *
     * @return 销售单号
     */
    private String waitForSalesOrderCompletion(CompanyBusinessRequest request, String currentCompany) {
        log.info("开始等待销售订单完成，业务ID: {}, 公司: {}", request.getBusinessId(), currentCompany);
        String businessId = request.getBusinessId();

        try {
            // 构建数据库查询参数
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER"));
            queryParameters.put("currentCompany", currentCompany);
            queryParameters.put("previousCompany", null); // 当前公司的销售订单，不需要前置公司

            // 构建统一轮询请求 - 使用Lombok Builder
            UniversalPollingRequest pollingRequest =
                    UniversalPollingRequest.builder()
                            .businessId(businessId)
                            .businessType("SALES_ORDER")
                            .dataSourceType(DataSourceType.LOCAL_DATABASE)
                            .companyCode(currentCompany)
                            .queryType("TEMPORAL_FLOW_ORDER_QUERY")
                            .queryParameters(queryParameters)
                            .completionCheckConfig("saleOrderNo:isNotBlank")
                            .build();

            // 执行统一轮询（使用静态方法保持确定性）
            UniversalPollingResult<Map<String, Object>> result = UniversalPollingWorkflow.universalPoll(pollingRequest);

            if (!result.isSuccess()) {
                throw BusinessProcessException.fromStep("销售订单完成确认失败: " + result.getMessage(),
                        "SALES_ORDER_POLLING_INCOMPLETE", true, getStepName(), currentCompany, businessId);
            }

            // 直接从轮询结果获取销售单号
            Map<String, Object> data = result.getData();
            String saleOrderNo = data != null ? (String) data.get("saleOrderNo") : null;

            // 存储到扩展属性
            Map<String, Object> extendedProperties = request.getExtendedProperties();
            if (extendedProperties == null) {
                extendedProperties = new HashMap<>();
            }
            extendedProperties.put("saleOrderNo", saleOrderNo);
            request.setExtendedProperties(extendedProperties);

            log.info("销售订单完成确认成功，业务ID: {}, 公司: {}, 销售单号: {}",
                    businessId, currentCompany, saleOrderNo);

            return saleOrderNo;

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("等待销售订单完成异常，业务ID: {}, 公司: {}", businessId, currentCompany, e);
            throw BusinessProcessException.fromStep("销售订单完成系统异常", "SALES_ORDER_POLLING_ERROR", true, getStepName(), currentCompany, businessId);
        }
    }

    /**
     * 等待采购订单完成
     * 使用统一轮询组件进行API轮询
     */
    private void waitForOrderCompletion(CompanyBusinessRequest request, String orderId) {
        log.info("开始等待采购订单完成，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);
        String businessId = request.getBusinessId();
        String companyCode = request.getTargetCompanyCode();

        try {
            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("purchaseOrderId", Integer.valueOf(orderId));
            apiParameters.put("orderId", Integer.valueOf(orderId));

            UniversalPollingRequest pollingRequest =
                    UniversalPollingRequest.builder()
                            .businessId(orderId)
                            .businessType("PURCHASE_ORDER")
                            .dataSourceType(DataSourceType.REMOTE_API)
                            .companyCode(companyCode)
                            .apiPath("/api/v1/purchaseorder/query.do")
                            .apiParameters(apiParameters)
                            .completionCheckConfig("data.validStatus:1")
                            .build();

            // 执行统一轮询（使用静态方法保持确定性）
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(pollingRequest);

            if (!finalResult.isSuccess()) {
                throw BusinessProcessException.fromStep("采购订单完成确认失败: " + finalResult.getMessage(), "PURCHASE_ORDER_POLLING_INCOMPLETE", true, getStepName(), companyCode, businessId);
            }

            log.info("采购订单完成确认成功，业务ID: {}, 订单ID: {}", businessId, orderId);

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("等待采购订单完成异常，业务ID: {}, 订单ID: {}", businessId, orderId, e);
            throw BusinessProcessException.fromStep("采购订单完成系统异常", "PURCHASE_ORDER_POLLING_ERROR", true, getStepName(), companyCode, businessId);
        }
    }

}
