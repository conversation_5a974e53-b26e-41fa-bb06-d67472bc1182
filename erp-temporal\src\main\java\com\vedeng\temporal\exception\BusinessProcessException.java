package com.vedeng.temporal.exception;

/**
 * 业务流程异常（精简优化版）
 *
 * 用于整个调用链路的业务逻辑异常处理，采用 fail-fast 模式
 *
 * 设计理念：
 * - 使用异常机制替代返回值错误处理
 * - 提供清晰的错误信息和错误码
 * - 支持业务上下文信息传递
 * - 智能重试机制，根据异常类型决定重试策略
 *
 * 精简说明：
 * - 移除冗余的构造函数，只保留核心2个
 * - 简化静态工厂方法，只保留实用的3个
 * - 移除无用的上下文链方法和判断方法
 * - 精简信息展示方法，提高可维护性
 *
 * <AUTHOR> 4.0 sonnet
 * @version 4.0 (精简优化版)
 * @since 2025-01-25
 */
public class BusinessProcessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 业务ID（用于日志追踪）
     */
    private final String businessId;

    /**
     * 失败的步骤名称
     */
    private final String stepName;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    /**
     * 异常发生的层次（ACTIVITY, STEP, PROCESS）
     */
    private final String layerName;

    /**
     * 公司代码（用于多公司场景）
     */
    private final String companyCode;

    /**
     * 操作阶段（用于更精确的异常定位）
     * 例如：CREATE_ORDER, SUBMIT_APPROVAL, EXECUTE_APPROVAL等
     */
    private final String operationPhase;

    /**
     * 上下文数据（用于异常诊断和恢复）
     * 包含异常发生时的关键业务数据
     */
    private final java.util.Map<String, Object> contextData;
    

    /**
     * 完整构造函数
     */
    public BusinessProcessException(String message, String errorCode, boolean retryable,
                               String businessId, String stepName, String layerName, 
                               String companyCode, String operationPhase, 
                               java.util.Map<String, Object> contextData, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.businessId = businessId;
        this.stepName = stepName;
        this.retryable = retryable;
        this.layerName = layerName;
        this.companyCode = companyCode;
        this.operationPhase = operationPhase;
        this.contextData = contextData != null ? new java.util.HashMap<>(contextData) : null;
    }

    /**
     * 简化构造函数（核心参数）
     */
    public BusinessProcessException(String message, String errorCode, boolean retryable) {
        this(message, errorCode, retryable, null, null, null, null, null, null, null);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 统一创建方法（替代 retryable/nonRetryable）
     */
    public static BusinessProcessException create(String message, String errorCode, boolean retryable) {
        return new BusinessProcessException(message, errorCode, retryable);
    }

    /**
     * 创建 Activity 层异常（支持操作阶段和上下文数据）
     */
    public static BusinessProcessException fromActivity(String message, String errorCode, 
                                                      boolean retryable, String activityName) {
        return new BusinessProcessException(message, errorCode, retryable, null, 
                                          activityName, "ACTIVITY", null, null, null, null);
    }



    /**
     * 创建 Step 层异常
     */
    public static BusinessProcessException fromStep(String message, String errorCode, boolean retryable,
                                               String stepName, String companyCode) {
        return new BusinessProcessException(message, errorCode, retryable, null, 
                                          stepName, "STEP", companyCode, null, null, null);
    }

    /**
     * 创建 Step 层异常（带业务ID）
     */
    public static BusinessProcessException fromStep(String message, String errorCode, boolean retryable,
                                               String stepName, String companyCode, String businessId) {
        return new BusinessProcessException(message, errorCode, retryable, businessId, 
                                          stepName, "STEP", companyCode, null, null, null);
    }

    /**
     * 创建 Step 层异常（完整版本，带操作阶段和上下文数据）
     */
    public static BusinessProcessException fromStep(String message, String errorCode, boolean retryable,
                                               String stepName, String companyCode, String businessId,
                                               String operationPhase, java.util.Map<String, Object> contextData) {
        return new BusinessProcessException(message, errorCode, retryable, businessId, 
                                          stepName, "STEP", companyCode, operationPhase, contextData, null);
    }


    /**
     * 创建 Process 层异常
     */
    public static BusinessProcessException fromProcess(String message, String errorCode, boolean retryable,
                                                  String processName) {
        return new BusinessProcessException(message, errorCode, retryable, null, 
                                          processName, "PROCESS", null, null, null, null);
    }

    /**
     * 创建 Process 层异常（带公司代码）
     */
    public static BusinessProcessException fromProcess(String message, String errorCode, boolean retryable,
                                                  String processName, String companyCode) {
        return new BusinessProcessException(message, errorCode, retryable, null, 
                                          processName, "PROCESS", companyCode, null, null, null);
    }
    
    // ========== Getter 方法 ==========

    public String getErrorCode() {
        return errorCode;
    }

    public String getBusinessId() {
        return businessId;
    }

    public String getStepName() {
        return stepName;
    }

    public boolean isRetryable() {
        return retryable;
    }

    public String getLayerName() {
        return layerName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public String getOperationPhase() {
        return operationPhase;
    }

    public java.util.Map<String, Object> getContextData() {
        return contextData != null ? new java.util.HashMap<>(contextData) : null;
    }

    /**
     * 简化的toString方法（包含新增字段）
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("BusinessProcessException{");
        sb.append("message='").append(getMessage()).append('\'');
        sb.append(", errorCode='").append(errorCode).append('\'');
        if (retryable) {
            sb.append(", retryable");
        }
        if (layerName != null) {
            sb.append(", layer=").append(layerName);
        }
        if (companyCode != null) {
            sb.append(", company=").append(companyCode);
        }
        if (operationPhase != null) {
            sb.append(", phase=").append(operationPhase);
        }
        if (contextData != null && !contextData.isEmpty()) {
            sb.append(", contextSize=").append(contextData.size());
        }
        sb.append('}');
        return sb.toString();
    }
}