package com.vedeng.temporal.notification;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Temporal工作流通知服务
 * 处理各类工作流失败的通知发送
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-10
 */
@Service
@Slf4j
public class TemporalNotificationService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private WxRobotService wxRobotService;
    
    @Autowired
    private NotificationConfig config;
    
    @Autowired
    private NotificationDeduplicator deduplicator;
    
    private ExecutorService executorService;
    
    @PostConstruct
    public void init() {
        // 根据配置决定线程池大小
        int threadPoolSize = config.isAsync() ? 3 : 1;
        executorService = Executors.newFixedThreadPool(threadPoolSize);
        log.info("通知服务初始化完成，异步模式: {}, 线程池大小: {}", config.isAsync(), threadPoolSize);
    }
    
    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
            log.info("通知服务已关闭");
        }
    }
    
    /**
     * 发送业务失败通知
     * 
     * @param context 通知上下文
     */
    public void sendBusinessFailureNotification(NotificationContext context) {
        if (!config.isEnabled()) {
            log.debug("通知功能未启用，跳过业务失败通知");
            return;
        }
        
        NotificationLevel level = NotificationLevel.BUSINESS;
        if (!deduplicator.shouldSend(context)) {
            return;
        }
        
        String message = StrUtil.format(TemporalMsgTemplate.BUSINESS_FAILURE,
                context.getBusinessId(),
                context.getBusinessType(),
                context.getTargetCompany(),
                context.getOperationName(),
                context.getErrorMessage(),
                LocalDateTime.now().format(DATE_FORMATTER),
                context.getDuration() != null ? context.getDuration() : 0);
        
        sendNotification(message, level, context);
    }
    
    /**
     * 发送技术异常通知
     * 
     * @param context 通知上下文
     */
    public void sendTechnicalErrorNotification(NotificationContext context) {
        if (!config.isEnabled()) {
            log.debug("通知功能未启用，跳过技术异常通知");
            return;
        }
        
        NotificationLevel level = NotificationLevel.TECHNICAL;
        if (!deduplicator.shouldSend(context)) {
            return;
        }
        
        String message = StrUtil.format(TemporalMsgTemplate.TECHNICAL_ERROR,
                context.getBusinessId(),
                context.getExceptionType(),
                context.getErrorMessage(),
                context.getOperationName(),
                LocalDateTime.now().format(DATE_FORMATTER),
                context.getDuration() != null ? context.getDuration() : 0);
        
        sendNotification(message, level, context);
    }
    
    /**
     * 发送工作流失败通知
     * 
     * @param context 通知上下文
     */
    public void sendWorkflowFailureNotification(NotificationContext context) {
        if (!config.isEnabled()) {
            log.debug("通知功能未启用，跳过工作流失败通知");
            return;
        }
        
        NotificationLevel level = NotificationLevel.WORKFLOW;
        if (!deduplicator.shouldSend(context)) {
            return;
        }
        
        String message = StrUtil.format(TemporalMsgTemplate.WORKFLOW_FAILURE,
                context.getBusinessId(), // 作为工作流ID
                context.getBusinessType(),
                context.getSourceCompany() + "→" + context.getTargetCompany(),
                context.getTargetCompany(),
                context.getErrorMessage(),
                LocalDateTime.now().format(DATE_FORMATTER),
                context.getOperationName());
        
        sendNotification(message, level, context);
    }
    
    /**
     * 发送通知
     * 
     * @param message 消息内容
     * @param level 通知级别
     * @param context 通知上下文
     */
    private void sendNotification(String message, NotificationLevel level, NotificationContext context) {
        String robotKey = config.getRobotKey();
        if (StrUtil.isBlank(robotKey)) {
            log.warn("机器人Key未配置，无法发送通知");
            return;
        }
        
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(message);
        
        if (config.isAsync()) {
            executorService.submit(() -> doSendNotification(robotKey, wxMsgDto, level, context));
        } else {
            doSendNotification(robotKey, wxMsgDto, level, context);
        }
    }
    
    /**
     * 实际发送通知
     */
    private void doSendNotification(String robotKey, WxMsgDto wxMsgDto, NotificationLevel level, NotificationContext context) {
        try {
            wxRobotService.send(robotKey, wxMsgDto);
            log.info("发送{}级别通知成功，业务ID: {}, 操作: {}", 
                level.getDescription(), context.getBusinessId(), context.getOperationName());
        } catch (Exception e) {
            log.error("发送{}级别通知失败，业务ID: {}, 操作: {}", 
                level.getDescription(), context.getBusinessId(), context.getOperationName(), e);
        }
    }
}
