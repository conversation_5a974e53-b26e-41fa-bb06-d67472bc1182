package com.vedeng.temporal.polling.universal.workflow;

import com.vedeng.temporal.polling.universal.activity.UniversalPollingActivity;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.config.TemporalProperties;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一轮询Workflow方法类
 * 
 * 提供基于Temporal Workflow的统一轮询方法，使用Activity执行轮询逻辑。
 * 这是整个统一轮询组件的核心类，实现了一个方法解决所有轮询场景的目标。
 * 
 * 核心特性：
 * - 真正的无限轮询，直到业务条件满足
 * - 基于Activity的轮询机制，通过Workflow.sleep()实现挂起等待
 * - 支持指数退避策略和快速重试
 * - 完整的监控和日志记录
 * - 支持超时和最大尝试次数限制
 * - 统一处理远程API和本地数据库两种数据源
 * 
 * 使用场景：
 * - 等待外部系统状态变化
 * - 等待业务流程完成
 * - 等待数据同步完成
 * - 等待审批流程结束
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@Slf4j
public class UniversalPollingWorkflow {
    
    /**
     * 统一轮询方法
     * 
     * 这是整个组件的核心方法，实现了统一处理所有轮询场景的目标。
     * 无论是远程API轮询还是本地数据库轮询，都通过这一个方法来处理。
     * 
     * 工作原理：
     * 1. 创建UniversalPollingActivity存根，配置重试策略
     * 2. 进入轮询循环，调用Activity执行单次查询
     * 3. 检查是否满足完成条件
     * 4. 如果未满足，使用Workflow.sleep()挂起等待
     * 5. 根据退避策略计算下次轮询间隔
     * 6. 重复直到条件满足或达到限制
     * 
     * 资源占用：
     * - 在Workflow.sleep()期间，工作流完全挂起，零资源占用
     * - 只有在执行查询时才会占用资源
     * - 支持进程重启后恢复轮询状态
     * 
     * @param request 统一轮询请求，包含数据源配置和条件检查器
     * @return 满足条件时的轮询结果
     * @throws RuntimeException 当达到超时或最大尝试次数限制时
     */
    public static UniversalPollingResult<Map<String, Object>> universalPoll(
            UniversalPollingRequest request) {
        
        log.info("开始统一轮询，请求: {}, 数据源: {}", 
                request.createRequestKey(), request.getDataSourceType());
        
        // 验证请求参数
        request.validate();
        
        // 获取轮询配置
        TemporalProperties temporalProperties = ErpSpringBeanUtil.getBean(TemporalProperties.class);
        TemporalProperties.PollingConfig config = temporalProperties.getPolling();
        
        // 创建Activity存根，配置重试策略
        ActivityOptions activityOptions = createActivityOptions();
        UniversalPollingActivity pollingActivity = Workflow.newActivityStub(
                UniversalPollingActivity.class, activityOptions);
        
        // 轮询状态变量
        Duration currentInterval = config.getInitialIntervalDuration();
        int attemptCount = 0;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime lastHeartbeat = startTime;

        log.info("统一轮询开始，初始间隔: {}, 数据源: {}", 
                currentInterval, request.getDataSourceType());
        
        while (true) {
            attemptCount++;
            
            // 检查最大尝试次数限制
            if (config.getMaxRetryCount() > 0 && attemptCount > config.getMaxRetryCount()) {
                String errorMsg = String.format("轮询达到最大尝试次数限制: %d, 请求: %s", 
                        config.getMaxRetryCount(), request.createRequestKey());
                log.error(errorMsg);
                
                LocalDateTime endTime = LocalDateTime.now();
                Duration totalDuration = Duration.between(startTime, endTime);
                return UniversalPollingResult.failure(errorMsg, attemptCount, totalDuration.toMillis());
            }
            
            // 检查最大等待时间限制
            Duration elapsed = Duration.between(startTime, LocalDateTime.now());
            if (elapsed.compareTo(config.getMaxTimeoutDuration()) > 0) {
                String errorMsg = String.format("轮询达到最大等待时间限制: %s, 请求: %s", 
                        config.getMaxTimeoutDuration(), request.createRequestKey());
                log.error(errorMsg);
                
                LocalDateTime endTime = LocalDateTime.now();
                Duration totalDuration = Duration.between(startTime, endTime);
                return UniversalPollingResult.timeout(errorMsg, attemptCount, totalDuration.toMillis());
            }
            
            try {
                if (config.isEnableVerboseLogging()) {
                    log.debug("执行第 {} 次轮询查询，请求: {}, 数据源: {}", 
                            attemptCount, request.createRequestKey(), request.getDataSourceType());
                }
                
                // 执行单次查询
                UniversalPollingResult<Map<String, Object>> result = pollingActivity.executeQuery(request);

                if (result.isSuccess()) {
                    // 查询成功且条件满足
                    Duration totalDuration = Duration.between(startTime, LocalDateTime.now());
                    log.info("统一轮询条件满足，结束轮询，请求: {}, 数据源: {}, 总尝试次数: {}, 总耗时: {}", 
                            request.createRequestKey(), request.getDataSourceType(), attemptCount, totalDuration);
                    
                    // 返回成功结果
                    return UniversalPollingResult.success(result.getData(), result.getMessage(), 
                            attemptCount, totalDuration.toMillis());
                    
                } else {
                    // 查询成功但条件未满足，或查询失败
                    if (config.isEnableVerboseLogging()) {
                        log.debug("轮询条件未满足，继续等待，请求: {}, 尝试次数: {}, 数据: {}", 
                                request.createRequestKey(), attemptCount, result.getData());
                    }
                }
                
            } catch (Exception e) {
                log.warn("查询异常，将重试，请求: {}, 尝试次数: {}, 错误: {}", 
                        request.createRequestKey(), attemptCount, e.getMessage());
            }
            
            // 发送心跳（如果需要）
            LocalDateTime now = LocalDateTime.now();
            if (Duration.between(lastHeartbeat, now).compareTo(config.getHeartbeatIntervalDuration()) > 0) {
                Duration elapsedSinceStart = Duration.between(startTime, now);
                String heartbeatMessage = String.format("统一轮询进行中，请求: %s, 数据源: %s, 尝试次数: %d, 已等待: %s", 
                        request.createRequestKey(), request.getDataSourceType(), attemptCount, elapsedSinceStart);
                log.info(heartbeatMessage);
                lastHeartbeat = now;
            }
            
            // 计算等待间隔（使用固定间隔，简化逻辑）
            Duration waitInterval = currentInterval;
            
            // 使用Workflow.sleep挂起等待
            if (config.isEnableVerboseLogging()) {
                log.debug("轮询等待 {} 后继续，请求: {}", waitInterval, request.createRequestKey());
            }
            Workflow.sleep(waitInterval);
            
            // 计算下次轮询间隔（指数退避）
            long nextIntervalMillis = (long) (currentInterval.toMillis() * config.getBackoffCoefficient());
            Duration maxInterval = config.getMaxIntervalDuration();
            currentInterval = Duration.ofMillis(Math.min(nextIntervalMillis, maxInterval.toMillis()));
        }
    }
    

    /**
     * 创建Activity选项
     * 只配置超时时间，不配置重试（由业务层统一控制重试逻辑）
     * 
     * @return Activity选项
     */
    private static ActivityOptions createActivityOptions() {
        return ActivityOptions.newBuilder()
                .setScheduleToCloseTimeout(Duration.ofMinutes(2))  // 单次查询最多2分钟
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(1)
                        .build())
                .build();
    }
}