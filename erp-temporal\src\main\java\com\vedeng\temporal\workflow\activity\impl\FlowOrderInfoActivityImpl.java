package com.vedeng.temporal.workflow.activity.impl;

import com.vedeng.temporal.workflow.activity.FlowOrderInfoActivity;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoQueryRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoResponse;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.service.FlowOrderInfoService;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.exception.BusinessProcessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 业务流程汇总信息活动实现类
 *
 * 实现特点：
 * - 符合 Temporal Activity 最佳实践
 * - 使用统一的 ExceptionHandler.handleBusinessException 进行异常处理
 * - 委托给业务服务层处理具体逻辑
 * - 集成通知机制和智能重试策略
 * - 与 UniversalBusinessTemplate 保持一致的异常处理标准
 *
 * 异常处理策略：
 * - 使用 ExceptionHandler.handleBusinessException 统一处理异常
 * - 技术异常（retryable=true）：自动触发 Temporal 重试
 * - 业务异常（retryable=false）：转换为 BusinessProcessException 抛出，不重试
 * - 集成完整的通知、监控、异常分类功能
 * - 符合 Temporal Activity 最佳实践
 *
 * <AUTHOR> 4.0 sonnet
 * @version 4.0 (统一异常处理标准，使用 handleBusinessException)
 * @since 2025-01-25
 */
@Slf4j
@Component
public class FlowOrderInfoActivityImpl implements FlowOrderInfoActivity {

    @Autowired
    private FlowOrderInfoService flowOrderInfoService;

    @Autowired
    private ExceptionHandler exceptionHandler;

    @Override
    public void updateFlowOrderInfo(FlowOrderInfoUpdateRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始更新汇总信息，节点ID: {}, 业务编号: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoNo());

            // 委托给业务服务层处理
            flowOrderInfoService.updateFlowOrderInfo(request);

            long duration = System.currentTimeMillis() - startTime;
            log.info("汇总信息更新成功，节点ID: {}, 耗时: {}ms",
                    request.getFlowNodeId(), duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("汇总信息更新异常，节点ID: {}, 耗时: {}ms",
                    request.getFlowNodeId(), duration, e);

            // 一行代码解决异常处理：集成通知、监控、异常分类等完整功能
            exceptionHandler.handleAndThrowActivityException(e, "updateFlowOrderInfo", 
                request.getFlowOrderInfoNo(), "SYSTEM");
        }
    }


    @Override
    public FlowOrderInfoResponse queryFlowOrderInfo(FlowOrderInfoQueryRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.debug("开始查询汇总信息，节点ID: {}, 业务编号: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoNo());

            // 参数验证
            validateQueryRequest(request);

            // 委托给业务服务层处理
            FlowOrderInfoResponse response = flowOrderInfoService.queryFlowOrderInfo(request);

            long duration = System.currentTimeMillis() - startTime;
            log.debug("查询汇总信息完成，节点ID: {}, 耗时: {}ms, 结果: {}",
                    request.getFlowNodeId(), duration, response != null ? "找到" : "未找到");

            return response;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("查询汇总信息异常，节点ID: {}, 耗时: {}ms",
                    request.getFlowNodeId(), duration, e);

            // 一行代码解决异常处理：集成通知、监控、异常分类等完整功能
            exceptionHandler.handleAndThrowActivityException(e, "queryFlowOrderInfo", 
                request.getFlowOrderInfoNo(), "SYSTEM");
            return null; // 永远不会执行到这里
        }
    }

    @Override
    public boolean existsByFlowNodeId(Long flowNodeId) {
        try {
            if (flowNodeId == null || flowNodeId <= 0) {
                return false;
            }

            log.debug("检查汇总信息是否存在，节点ID: {}", flowNodeId);

            boolean exists = flowOrderInfoService.existsByFlowNodeId(flowNodeId);

            log.debug("汇总信息存在性检查完成，节点ID: {}, 结果: {}", flowNodeId, exists);

            return exists;

        } catch (Exception e) {
            log.error("检查汇总信息存在性异常，节点ID: {}", flowNodeId, e);

            // 一行代码解决异常处理：集成通知、监控、异常分类等完整功能
            exceptionHandler.handleAndThrowActivityException(e, "existsByFlowNodeId", 
                String.valueOf(flowNodeId), "SYSTEM");
            return false; // 永远不会执行到这里
        }
    }



    // ==================== 私有方法 ====================



    /**
     * 验证查询请求参数
     */
    private void validateQueryRequest(FlowOrderInfoQueryRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException(request.getValidationError());
        }
    }


}
