package com.vedeng.temporal.workflow.process;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.workflow.activity.*;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.exception.ErrorClassifier;
import com.vedeng.temporal.workflow.step.impl.InventoryReceiptStepV2;
import com.vedeng.temporal.workflow.step.impl.PurchaseOrderStep;
import com.vedeng.temporal.workflow.step.impl.SalesOrderStep;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 采购-销售-库存流程服务 - 新架构版本
 * 基于AbstractBusinessProcess重构，使用统一的执行框架
 *
 * 架构升级说明：
 * - 使用 SalesOrderStepV2 替代 SalesOrderStep（新架构）
 * - 使用 PurchaseOrderStep V4.0（新架构，参考 SalesOrderStepV2）
 * - InventoryReceiptStep 保持原架构
 * - 每个步骤都使用独立的 Activity 接口
 *
 * 执行特点：
 * - 正序执行（A → B → C → D）
 * - 每个公司等待前一个公司完成基础步骤
 * - 包含销售订单V2、采购订单V2、入库单三个步骤
 * - 使用AbstractBusinessProcess的统一框架
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0 (新架构升级版，SalesOrderStepV2 + PurchaseOrderStep V4.0)
 * @since 2025-01-21
 */
@Slf4j
public class PurchaseSalesInventoryProcess extends AbstractBusinessProcess {


    private final SalesOrderActivity salesOrderActivity;

    private final PurchaseOrderActivity purchaseOrderActivity;

    private final PollingActivity pollingActivity;

    private final InventoryReceiptActivity inventoryReceiptActivity;

    public PurchaseSalesInventoryProcess(PurchaseOrderActivity purchaseOrderActivity,
                                         SalesOrderActivity salesOrderActivity,
                                         PollingActivity pollingActivity,
                                         NotificationActivity notificationActivity,
                                         FlowOrderInfoActivity flowOrderInfoActivity,
                                         CompanySequenceActivity companySequenceActivity,
                                         InventoryReceiptActivity inventoryReceiptActivity,
                                         ErrorClassifier errorClassifier) {

        // 调用父类构造函数
        super(notificationActivity, flowOrderInfoActivity, companySequenceActivity, pollingActivity, errorClassifier);

        // 保存Activity依赖
        this.purchaseOrderActivity = purchaseOrderActivity;
        this.salesOrderActivity = salesOrderActivity;
        this.pollingActivity = pollingActivity;
        this.inventoryReceiptActivity = inventoryReceiptActivity;
    }

    @Override
    protected ExecutionMode getExecutionMode() {
        return ExecutionMode.SEQUENTIAL;
    }

    @Override
    protected boolean isReverseOrder() {
        return false; // 正序执行
    }

    @Override
    protected List<BusinessStep> getBusinessSteps() {
        // 采购-销售-库存流程包含三个步骤：销售单V2 → 采购单V2 → 入库单
        return Arrays.asList(
            //new SalesOrderStep(salesOrderActivity, pollingActivity),
            new PurchaseOrderStep(purchaseOrderActivity)
            //new InventoryReceiptStepV2(inventoryReceiptActivity, pollingActivity)
        );
    }

    @Override
    protected String getProcessName() {
        return "采购-销售-库存流程";
    }



    /**
     * 执行采购-销售-库存流程
     * 使用AbstractBusinessProcess统一框架
     *
     * @param request 业务请求
     * @param companySequence 公司执行序列
     * @return 执行结果
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        return super.execute(request, companySequence);
    }
}
