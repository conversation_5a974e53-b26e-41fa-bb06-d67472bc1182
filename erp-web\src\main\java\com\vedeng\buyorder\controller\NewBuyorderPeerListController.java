package com.vedeng.buyorder.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.common.enums.SpuTypeEnum;
import com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchDetailVo;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryDirectAttachmentDto;
import com.vedeng.erp.buyorder.service.NewBuyorderPeerListService;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.*;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.AttachmentService;
import com.wms.dto.PurchaseDeliveryDirectBatchDetailDto;
import com.wms.service.other.DeliveryDirectSaleorderChooseServiceImpl;
import com.wms.service.other.DoPutDeliveryDirectPeerListServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 直发采购同行单维护
 */
@Slf4j
@Controller
@RequestMapping("/order/newBuyorderPeerList")
public class NewBuyorderPeerListController extends BaseController {

    @Autowired
    private BuyorderService buyorderService;

    @Value("${oss_http}")
    private String ossHttp;

    @Value("${old_buyOrder_time}")
    private String oldBuyOrderTime;

    @Autowired
    private NewBuyorderPeerListService newBuyorderPeerListService;

    @Autowired
    private DoPutDeliveryDirectPeerListServiceImpl doPutDeliveryDirectPeerListServiceImpl;

    @Autowired
    private DeliveryDirectSaleorderChooseServiceImpl deliveryDirectSaleorderChooseServiceImpl;

    @Autowired
    private AttachmentService attachmentService;

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 5;
    private static final long KEEP_ALIVE_TIME = 60L;

    /**
     * 声明异步线程池
     */
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 4, true),
            new ThreadFactoryBuilder().setNameFormat("newBuyorderPeerList-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());
    /**
     * 采购同行单编辑页面情页面
     *
     * @param request
     * @param buyorder
     * @return
     */
    @RequestMapping(value = "/editPeerListView")
    @NoNeedAccessAuthorization
    public ModelAndView viewBuyOrderDetail(HttpServletRequest request, Buyorder buyorder) {

        User user = getSessionUser(request);
        ModelAndView mav = new ModelAndView("orderstream/buyorder/buyorder_tx_detail");
        BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);
        bv.setCompanyId(null);
        bv = buyorderService.getSaleBuyNumByAjax(bv, user);
        List<Express> expressList = bv.getExpressList();
        Long time = null;
        if (!StringUtils.isEmpty(oldBuyOrderTime)) {
            time  = Long.valueOf(oldBuyOrderTime);
        }
        if (time != null && bv.getAddTime() < time) {
            if (!CollectionUtils.isEmpty(expressList)) {
                List<Integer> collect = expressList.stream().map(Express::getExpressId).collect(Collectors.toList());
                mav.addObject("expressIds", collect);
            }
            bv.setExpressList(expressList);
            mav.addObject("bv", bv);
        } else {
            List<Express> collect1 = expressList.stream().filter(c -> {
                return c.getArrivalStatus().equals(2);
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)) {
                List<Integer> collect = collect1.stream().map(Express::getExpressId).collect(Collectors.toList());
                mav.addObject("expressIds", collect);
            }
            bv.setExpressList(collect1);
            mav.addObject("bv", bv);
        }

        return mav;
    }

    /**
     * HOTFIX layui 问题
     * 采购同行单编辑页面情页面
     *
     * @param request
     * @param buyorder
     * @return
     */
    @RequestMapping(value = "/editPeerListView2")
    @NoNeedAccessAuthorization
    public ModelAndView viewBuyOrderDetail2(HttpServletRequest request, Buyorder buyorder) {

        User user = getSessionUser(request);
        ModelAndView mav = new ModelAndView("orderstream/buyorder/buyorder_tx_detail2");
        BuyorderVo bv = buyorderService.getBuyorderVoDetailNew(buyorder, user);
        bv.setCompanyId(null);
        bv = buyorderService.getSaleBuyNumByAjax(bv, user);
        List<Express> expressList = bv.getExpressList();
        Long time = null;
        if (!StringUtils.isEmpty(oldBuyOrderTime)) {
            time  = Long.valueOf(oldBuyOrderTime);
        }
        if (time != null && bv.getAddTime() < time) {
            if (!CollectionUtils.isEmpty(expressList)) {
                List<Integer> collect = expressList.stream().map(Express::getExpressId).collect(Collectors.toList());
                mav.addObject("expressIds", collect);
            }
            bv.setExpressList(expressList);
            mav.addObject("bv", bv);
        } else {
            List<Express> collect1 = expressList.stream().filter(c -> {
                return c.getArrivalStatus().equals(2);
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)) {
                List<Integer> collect = collect1.stream().map(Express::getExpressId).collect(Collectors.toList());
                mav.addObject("expressIds", collect);
            }
            bv.setExpressList(collect1);
            mav.addObject("bv", bv);
        }

        return mav;
    }

    /**
     * excel上传页面
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/uploadExcelView")
    @NoNeedAccessAuthorization
    public ModelAndView uploadExcelView(HttpServletRequest request, Integer expressId) {

        ModelAndView mav = new ModelAndView("orderstream/buyorder/buyorder_tx_detail_upload_excel");
        mav.addObject("expressId",expressId);
        return mav;
    }

    /**
     * HOTFIX layui 大数据量卡顿问题
     * excel上传页面
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/uploadExcelView2")
    @NoNeedAccessAuthorization
    public ModelAndView uploadExcelView2(HttpServletRequest request, Integer expressId,@RequestParam("buyOrderId")Integer buyOrderId) {

        ModelAndView mav = new ModelAndView("orderstream/buyorder/buyorder_tx_detail_upload_excel2");
        mav.addObject("expressId",expressId);
        mav.addObject("buyOrderId",buyOrderId);
        return mav;
    }
    /**
     * 同行单上传图片页面
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/uploadImgView")
    @NoNeedAccessAuthorization
    public ModelAndView uploadImgView(HttpServletRequest request) {

        ModelAndView mav = new ModelAndView("/orderstream/buyorder/buyorder_tx_detail_upload_img");
        return mav;
    }

    /**
     * 可添加的采购同行单的采购商品信息详情页面
     *
     * @param request
     * @param express
     * @return
     */
    @ResponseBody
    @RequestMapping("/getNeedEditBuyGoods")
    @NoNeedAccessAuthorization
    public Map<String, Object> getNeedEditBuyGoods(HttpServletRequest request, Express express) {

        List<PeerListBuyorderGoods> needEditBuyGoods = newBuyorderPeerListService.getNeedEditBuyGoods(express.getExpressId());
        Map<String, Object> result = new HashMap<>(4);
        result.put("code", 0);
        result.put("msg", "");
        result.put("count",needEditBuyGoods==null?0:needEditBuyGoods.size());
        result.put("data",needEditBuyGoods);
        return result;
    }

    /**
     * 校验
     */
    @ResponseBody
    @RequestMapping("/checkDetails")
    @NoNeedAccessAuthorization
    public ResultInfo checkDetails(@RequestBody List<PurchaseDeliveryDirectBatchDetailVo> data) {

        Map<String, Object> stringObjectMap = newBuyorderPeerListService.checkDetails(data);
        return ResultInfo.success(stringObjectMap);
    }

    /**
     * 保存采购同行单数据
     */
    @ResponseBody
    @RequestMapping("/saveDetails")
    @NoNeedAccessAuthorization
    public ResultInfo saveDetails(@RequestBody HttpServletRequest request,PurchaseDeliveryDirectBatchInfoVo data) {

        try {
            User user = getSessionUser(request);
            log.info("直发同行单 saveDetails 入参：{}", JSON.toJSONString(data));
            Date now = new Date();
            data.setAddTime(now);
            data.setModTime(now);
            data.setCreator(user == null ? null : user.getUserId());
            data.setUpdater(user == null ? null : user.getUserId());
            Map<String, Object> stringObjectMap = newBuyorderPeerListService.checkDetails(data.getList());
            log.info("直发同行单 checkDetails 结果：{}", JSON.toJSON(stringObjectMap));
            if (stringObjectMap.get("code").equals(200)) {
                try {
                    newBuyorderPeerListService.savePurchaseDelivery(data);
                } catch (Exception e) {
                    log.error("saveDetails 保存出错", e);
                    return ResultInfo.error(e.getMessage());
                }
                // 下发wms
                try {
                    List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(data.getBuyorderId(), false);
                    log.info("直发采购单入库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                    doPutDeliveryDirectPeerListServiceImpl.doPutPurchaseOrderMethod(purchaseDeliveryDirectBatchDetails, user);
                } catch (Exception e) {
                    log.error("直发采购单入库下发wms失败:", e);
                }

                try {

                    // 查寻采购单关联的所有销售单
                    List<Integer> saleorderIdListByBuyorderId = newBuyorderPeerListService.getSaleorderIdListByBuyorderId(data.getBuyorderId());
                    if (!CollectionUtils.isEmpty(saleorderIdListByBuyorderId)) {
                        // 查询所有采购单
                        List<Integer> buyorderIdListBySaleorderIds = newBuyorderPeerListService.getBuyorderIdListBySaleorderIds(saleorderIdListByBuyorderId);
                        if (!CollectionUtils.isEmpty(buyorderIdListBySaleorderIds)) {
                            buyorderIdListBySaleorderIds.add(data.getBuyorderId());
                            // 去重
                            List<Integer> collect = buyorderIdListBySaleorderIds.stream().distinct().collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(collect) && collect.size() == 1) {
                                List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(data.getBuyorderId(), true);
                                log.info("直发采购单出库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                                deliveryDirectSaleorderChooseServiceImpl.putSaleOrderOutput(purchaseDeliveryDirectBatchDetails, user);
                            } else if (!CollectionUtils.isEmpty(collect)&&collect.size()>1)  {
                                List<PurchaseDeliveryDirectBatchDetailDto> result = new ArrayList<>();
                                log.info("直发采购单关联销售有多个采购单：{}",JSON.toJSONString(collect));
                                for (Integer buyId : collect) {
                                    List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyId, true);
                                    if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                                        result.addAll(purchaseDeliveryDirectBatchDetails);
                                    }
                                }
                                log.info("直发采购单关联销售有多个采购单触发采购单{} 直发采购单出库下发wms信息:{}", data.getBuyorderId(),JSON.toJSONString(result));
                                deliveryDirectSaleorderChooseServiceImpl.putSaleOrderOutput(result, user);
                            }
                        }
                    }


                } catch (Exception e) {
                    log.error("直发采购单出库下发wms失败:", e);
                }
            }

            return ResultInfo.success(stringObjectMap);
        } catch (Exception e) {
            log.error("直发采购同行单保存失败：", e);
            return ResultInfo.error(e.getMessage());
        }
    }

    /**
     * 解析上传的excel
     *
     * @param file excel 文件
     * @return
     */
    @RequestMapping("/analysisExcel")
    @ResponseBody
    @NoNeedAccessAuthorization
    public Map<String, Object> analysisExcel(@RequestParam("file") MultipartFile file,@RequestParam("expressId") Integer expressId) {

        Map<String, Object> result = new HashMap<>(4);
        Map<String, Object> data = new HashMap<>(3);
        try {
            List<PeerListBuyorderGoods> peerListBuyorderGoods = newBuyorderPeerListService.analysisExcel(file,expressId);
            result.put("code", 0);
            result.put("msg", "");
            data.put("count",peerListBuyorderGoods==null?0:peerListBuyorderGoods.size());
            data.put("code", 0);
            data.put("data", peerListBuyorderGoods);
            data.put("msg", "");
            result.put("data",data);
        } catch (Exception e) {
            log.error("解析同行单excel异常：{}",e);
            result.put("code", 500);
            result.put("msg", e.getMessage());
        }

        return result;
    }


    /**
     * 解析上传的excel
     *
     * @param file excel 文件
     * @return
     */
    @RequestMapping("/analysisExcel2")
    @ResponseBody
    @NoNeedAccessAuthorization
    public Map<String, Object> analysisExcel2(@RequestParam("file") MultipartFile file,@RequestParam("expressId") Integer expressId,@RequestParam("buyOrderId")Integer buyOrderId,HttpServletRequest request) {

        User sessionUser = getSessionUser(request);

        Map<String, Object> result = new HashMap<>(4);
        Map<String, Object> data = new HashMap<>(3);
        try {
            List<PeerListBuyorderGoods> peerListBuyorderGoods = newBuyorderPeerListService.analysisExcel(file,expressId);
            PurchaseDeliveryDirectBatchInfoVo saveData = new PurchaseDeliveryDirectBatchInfoVo();
            saveData.setAddTime(new Date());
            List<PurchaseDeliveryDirectBatchDetailVo> saveDetail = new ArrayList<>();
            if (sessionUser != null) {
                saveData.setCreator(sessionUser.getUserId());
                saveData.setBuyorderId(buyOrderId);

                if (!CollectionUtils.isEmpty(peerListBuyorderGoods)) {
                    for (PeerListBuyorderGoods per : peerListBuyorderGoods) {
                        PurchaseDeliveryDirectBatchDetailVo purchaseDeliveryDirectBatchDetailVo = new PurchaseDeliveryDirectBatchDetailVo();
                        BeanUtils.copyProperties(per, purchaseDeliveryDirectBatchDetailVo);
                        purchaseDeliveryDirectBatchDetailVo.setSkuName(per.getGoodsName());
                        purchaseDeliveryDirectBatchDetailVo.setUnit(per.getUnitName());
                        purchaseDeliveryDirectBatchDetailVo.setProductCompany(per.getManufacturerName());
                        purchaseDeliveryDirectBatchDetailVo.setProductionLicence(per.getProductCompanyLicence());
                        purchaseDeliveryDirectBatchDetailVo.setRegisterNumber(per.getRegistrationNumber());
                        // TODO
                        if ("1".equals(per.getMedicalInstrumentCatalogIncluded())&&(SpuTypeEnum.EQUIPMENT.getCode().equals(per.getSpuType()))||(!"1".equals(per.getMedicalInstrumentCatalogIncluded())&&per.getIsManageVedengCode()!=null&&per.getIsManageVedengCode().equals(1)&&SpuTypeEnum.EQUIPMENT.getCode().equals(per.getSpuType()))) {
                            purchaseDeliveryDirectBatchDetailVo.setUnionSequence(1);
                        } else {
                            purchaseDeliveryDirectBatchDetailVo.setUnionSequence(0);
                        }
                        saveDetail.add(purchaseDeliveryDirectBatchDetailVo);
                    }
                }
            } else {
                throw new ServiceException("请重新登录");
            }
            peerListBuyorderGoods.clear();


            if (!CollectionUtils.isEmpty(saveDetail)) {
                saveData.setList(saveDetail);
                newBuyorderPeerListService.checkDetailsExcel(saveDetail);

                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    newBuyorderPeerListService.savePurchaseDelivery(saveData);

                    try {
                        List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyOrderId, false);
                        log.info("直发采购单入库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                        doPutDeliveryDirectPeerListServiceImpl.doPutPurchaseOrderMethod(purchaseDeliveryDirectBatchDetails, sessionUser);
                    } catch (Exception e) {
                        log.error("直发采购单入库下发wms失败:", e);
                    }

                    try {

                        // 查寻采购单关联的所有销售单
                        List<Integer> saleorderIdListByBuyorderId = newBuyorderPeerListService.getSaleorderIdListByBuyorderId(buyOrderId);
                        if (!CollectionUtils.isEmpty(saleorderIdListByBuyorderId)) {
                            // 查询所有采购单
                            List<Integer> buyorderIdListBySaleorderIds = newBuyorderPeerListService.getBuyorderIdListBySaleorderIds(saleorderIdListByBuyorderId);
                            if (!CollectionUtils.isEmpty(buyorderIdListBySaleorderIds)) {
                                buyorderIdListBySaleorderIds.add(buyOrderId);
                                // 去重
                                List<Integer> collect = buyorderIdListBySaleorderIds.stream().distinct().collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(collect) && collect.size() == 1) {
                                    List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyOrderId, true);
                                    log.info("直发采购单出库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                                    deliveryDirectSaleorderChooseServiceImpl.putSaleOrderOutput(purchaseDeliveryDirectBatchDetails, sessionUser);
                                } else if (!CollectionUtils.isEmpty(collect)&&collect.size()>1)  {
                                    List<PurchaseDeliveryDirectBatchDetailDto> result2 = new ArrayList<>();
                                    log.info("直发采购单关联销售有多个采购单：{}",JSON.toJSONString(collect));
                                    for (Integer buyId : collect) {
                                        List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyId, true);
                                        if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                                            result2.addAll(purchaseDeliveryDirectBatchDetails);
                                        }
                                    }
                                    log.info("直发采购单关联销售有多个采购单触发采购单{} 直发采购单出库下发wms信息:{}", buyOrderId,JSON.toJSONString(result));
                                    deliveryDirectSaleorderChooseServiceImpl.putSaleOrderOutput(result2, sessionUser);
                                }
                            }
                        }


                    } catch (Exception e) {
                        log.error("直发采购单出库下发wms失败:", e);
                    }
                }, executor);

                completableFuture.exceptionally(e -> {
                    log.error("大数据量保存失败", e);
                    return null;
                });

            } else {
                throw new ServiceException("未解析到数据");
            }

            result.put("code", 0);
            result.put("msg", "");
//            data.put("count",peerListBuyorderGoods==null?0:peerListBuyorderGoods.size());
            data.put("code", 0);
//            data.put("data", peerListBuyorderGoods);
            data.put("msg", "");
            result.put("data",data);
        } catch (Exception e) {
            log.error("解析同行单excel异常：{}",e);
            result.put("code", 500);
            result.put("msg", e.getMessage());
        }

        return result;
    }

    /**
     * 同行单模板导出
     * @param response
     * @param express
     * @param request
     * @throws IOException
     */
    @RequestMapping("/excelTemplateDownload")
    @NoNeedAccessAuthorization
    public void excelTemplateDownload(HttpServletResponse response, Express express,HttpServletRequest request) throws IOException {

        User user = getSessionUser(request);
        com.vedeng.erp.buyorder.domain.entity.Buyorder excelOutData = newBuyorderPeerListService.getExcelOutData(express);
        try {
            String classpath = this.getClass().getResource("/").getPath();
            String templateFileName = classpath+"/template/直发同行单模板2.xlsx";

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templateFileName).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 这里注意 入参用了forceNewRow 代表在写入list的时候不管list下面有没有空行 都会创建一行，然后下面的数据往后移动。默认 是false，会直接使用下一行，如果没有则创建。
            // forceNewRow 如果设置了true,有个缺点 就是他会把所有的数据都放到内存了，所以慎用
            // 简单的说 如果你的模板有list,且list不是最后一行，下面还有数据需要填充 就必须设置 forceNewRow=true 但是这个就会把所有数据放到内存 会很耗内存
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(excelOutData.getNeedEditBuyGoods(), fillConfig, writeSheet);
            excelWriter.fill(excelOutData, fillConfig, writeSheet);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileName = URLEncoder.encode("直发同行单模板-"+excelOutData.getBuyorderNo()+"-"+express.getExpressId(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter.finish();
        } catch (Exception e) {
            log.error("写入失败", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<String, String>();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }


    }


    /**
     * <AUTHOR>
     * @desc 打开同行单附件预览弹窗
     * @param purchaseDeliveryDirectBatchInfoId
     * @param request
     * @return
     */
    @RequestMapping("/preViewAttachment")
    @NoNeedAccessAuthorization
    public ModelAndView preViewAttachment(Integer purchaseDeliveryDirectBatchInfoId,HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("orderstream/buyorder/preview_direct_file");
        User user = getSessionUser(request);
        //根据采购单同行单批次信息查询附件信息
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(SysOptionConstant.ID_460);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4080);
        attachment.setRelatedId(purchaseDeliveryDirectBatchInfoId);
        List<Attachment> attachmentList = attachmentService.queryAttachmentList(attachment);
        mv.addObject("attachmentList",attachmentList);
        mv.addObject("oss_http",ossHttp);
        return mv;
    }

    /**
     * <AUTHOR>
     * @desc 重新上传同行单弹窗
     * @param purchaseDeliveryDirectBatchInfoId
     * @param request
     * @return
     */
    @RequestMapping("/preAddDeliveryDirectAttachment")
    @FormToken(save = true)
    @NoNeedAccessAuthorization
    public ModelAndView preAddDeliveryDirectAttachment(Integer purchaseDeliveryDirectBatchInfoId,HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("orderstream/buyorder/upload_direct_file");
        User user = getSessionUser(request);
        mv.addObject("user",user);
        mv.addObject("purchaseDeliveryDirectBatchInfoId",purchaseDeliveryDirectBatchInfoId);
        return mv;
    }

    /**
     * <AUTHOR>
     * @desc 保存采购直发同行单信息
     * @param purchaseDeliveryDirectAttachmentDto
     * @param request
     * @return
     */
    @FormToken(remove = true)
    @RequestMapping("/saveAddDeliveryDirectAttachment")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo saveAddDeliveryDirectAttachment(PurchaseDeliveryDirectAttachmentDto purchaseDeliveryDirectAttachmentDto,HttpServletRequest request){
        User user = getSessionUser(request);
        ResultInfo resultInfo = newBuyorderPeerListService.savePurchaseDeliveryDirectAttachment(purchaseDeliveryDirectAttachmentDto,user);
        return resultInfo;
    }

    /**
     * 根据采购单ID查询同行单信息
     *
     * @param buyorderId 采购单ID
     * @return 同行单信息列表
     */
    @ResponseBody
    @RequestMapping("/queryPeerList")
    @NoNeedAccessAuthorization
    public ResultInfo queryPeerList(@RequestParam("buyorderId") Integer buyorderId) {
        log.info("查询采购单同行单信息，buyorderId: {}", buyorderId);
        
        try {
            PurchaseDeliveryDirectBatchInfoVo peerList = newBuyorderPeerListService.queryPeerList(buyorderId);
            return ResultInfo.success("查询成功", peerList);
        } catch (Exception e) {
            log.error("查询采购单同行单信息失败，buyorderId: {}", buyorderId, e);
            return ResultInfo.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据采购单ID查询出入库记录并组装返回同行单创建入参
     *
     * @param buyorderId 采购单ID
     * @return 同行单创建入参
     */
    @ResponseBody
    @RequestMapping("/queryStockRecords")
    @NoNeedAccessAuthorization
    public ResultInfo queryStockRecords(@RequestParam("buyorderId") Integer buyorderId) {
        log.info("查询采购单出入库记录并组装同行单创建入参，buyorderId: {}", buyorderId);
        
        try {
            PurchaseDeliveryDirectBatchInfoVo stockRecords = newBuyorderPeerListService.queryStockRecords(buyorderId);
            return ResultInfo.success("查询成功", stockRecords);
        } catch (Exception e) {
            log.error("查询采购单出入库记录并组装同行单创建入参失败，buyorderId: {}", buyorderId, e);
            return ResultInfo.error("查询失败: " + e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping("/queryPeerRecords")
    @NoNeedAccessAuthorization
    public ResultInfo queryPeerRecords(@RequestParam("buyorderId") Integer buyorderId) {
        log.info("查询采购单同行单，buyorderId: {}", buyorderId);
        try {
            PurchaseDeliveryDirectBatchInfoVo peerRecords = newBuyorderPeerListService.queryPeerRecords(buyorderId);
            log.info("[queryPeerRecords]查询成功，peerRecords: {}", JSON.toJSON(peerRecords));
            return ResultInfo.success("[queryPeerRecords]查询成功", peerRecords);
        } catch (Exception e) {
            log.error("查询采购单出入库记录并组装同行单创建入参失败，buyorderId: {}", buyorderId, e);
            return ResultInfo.error("查询失败: " + e.getMessage());
        }
    }
}
