package com.vedeng.temporal.workflow.step.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.workflow.activity.SalesOrderActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.exception.BusinessProcessException;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 销售订单步骤 V2 - 新架构版本
 * <p>
 * 架构优化说明：
 * - 使用新的 SalesOrderActivity 替代 SalesOrderFlow
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：创建 → 提交 → 审核 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * <p>
 * 业务流程：
 * 1. createSalesOrder - 创建销售订单，获取订单ID
 * 2. submitSalesOrderForApproval - 提交审核，传递订单ID
 * 3. approveSalesOrder - 审核通过，传递订单ID
 * 4. waitForCompletion - 等待订单完成（可选）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (新架构版本)
 * @since 2025-01-18
 */
@Slf4j
public class SalesOrderStep implements BusinessStep {

    private final SalesOrderActivity salesOrderActivity;

    public SalesOrderStep(SalesOrderActivity salesOrderActivity) {
        this.salesOrderActivity = salesOrderActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        log.info("开始执行销售订单步骤V2，业务ID: {}, 目标公司: {}",
                request.getBusinessId(), request.getTargetCompanyCode());

        try {
            // 1. 创建销售订单
            CompanyBusinessResponse createResult = salesOrderActivity.createSalesOrder(request);
            if (!createResult.getSuccess()) {
                log.error("创建销售订单失败，业务ID: {}, 错误: {}",
                        request.getBusinessId(), createResult.getMessage());
                return createResult;
            }

            String orderId = createResult.getGeneratedDocumentId();
            log.info("销售订单创建成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 2. 提交审核（传递订单ID）
            CompanyBusinessRequest submitRequest = buildRequestWithOrderId(request, orderId);
            CompanyBusinessResponse submitResult = salesOrderActivity.submitSalesOrderForApproval(submitRequest);
            if (!submitResult.getSuccess()) {
                log.error("提交销售订单审核失败，业务ID: {}, 订单ID: {}, 错误: {}",
                        request.getBusinessId(), orderId, submitResult.getMessage());
                return submitResult;
            }

            log.info("销售订单提交审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 3. 审核通过（传递订单ID）
            CompanyBusinessRequest approveRequest = buildRequestWithOrderId(request, orderId);
            CompanyBusinessResponse approveResult = salesOrderActivity.approveSalesOrder(approveRequest);
            if (!approveResult.getSuccess()) {
                log.error("审核销售订单失败，业务ID: {}, 订单ID: {}, 错误: {}",
                        request.getBusinessId(), orderId, approveResult.getMessage());
                return approveResult;
            }

            log.info("销售订单审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

            // 4. 等待订单完成
            waitForOrderCompletion(request, orderId);

            // 5. 返回成功结果，包含订单ID
            log.info("销售订单步骤V2执行完成，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);
            return CompanyBusinessResponse.success("销售订单流程执行成功", orderId);

        } catch (Exception e) {
            log.error("销售订单步骤V2执行异常，业务ID: {}", request.getBusinessId(), e);
            return CompanyBusinessResponse.failure("销售订单流程执行失败: " + e.getMessage(), "STEP_EXECUTION_ERROR");
        }
    }

    @Override
    public String getStepName() {
        return "销售订单步骤V2";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.SALES_ORDER;
    }


    @Override
    public String getStepDescription() {
        return "执行销售订单完整流程：创建订单 → 提交审核 → 审核通过";
    }

    // ========== 私有辅助方法 ==========

    /**
     * 构建包含订单ID的请求
     */
    private CompanyBusinessRequest buildRequestWithOrderId(CompanyBusinessRequest originalRequest, String orderId) {
        // 解析原有的业务数据
        Map<String, Object> businessData = new HashMap<>();
        if (originalRequest.getBusinessData() != null) {
            try {
                businessData = JSON.parseObject(originalRequest.getBusinessData(), Map.class);
            } catch (Exception e) {
                log.warn("解析原有业务数据失败，使用空Map: {}", originalRequest.getBusinessData(), e);
            }
        }

        // 添加订单ID
        businessData.put("orderId", orderId);

        // 构建新的请求
        return CompanyBusinessRequest.builder()
                .businessId(originalRequest.getBusinessId())
                .businessType(originalRequest.getBusinessType())
                .sourceCompanyCode(originalRequest.getSourceCompanyCode())
                .targetCompanyCode(originalRequest.getTargetCompanyCode())
                .workflowExecutionId(originalRequest.getWorkflowExecutionId())
                .flowNodeId(originalRequest.getFlowNodeId())
                .businessData(JSON.toJSONString(businessData))
                .build();
    }


    /**
     * 等待订单完成
     * 使用统一轮询组件进行API轮询
     */
    private void waitForOrderCompletion(CompanyBusinessRequest request, String orderId) {
        log.info("开始等待销售订单完成，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        try {
            // 构建API参数
            Map<String, Object> apiParameters = new HashMap<>();
            apiParameters.put("saleOrderId", Integer.valueOf(orderId));

            // 构建统一轮询请求
            UniversalPollingRequest pollingRequest = UniversalPollingRequest.builder()
                    .businessId(request.getBusinessId())
                    .businessType(request.getBusinessType())
                    .dataSourceType(DataSourceType.REMOTE_API)
                    .companyCode(request.getTargetCompanyCode())
                    .apiPath("/api/v1/saleorder/query.do")
                    .apiParameters(apiParameters)
                    // 销售订单完成条件：审核通过 AND 支付金额大于0
                    .completionCheckConfig("VALID_TIME:1")
                    .build();

            // 使用 UniversalPollingWorkflow 等待订单完成
            UniversalPollingResult<Map<String, Object>> finalResult = UniversalPollingWorkflow.universalPoll(pollingRequest);

            // 验证最终状态
            if (!finalResult.isSuccess()) {
                log.error("销售订单完成确认失败，业务ID: {}, 订单ID: {}, 错误信息: {}",
                        request.getBusinessId(), orderId, finalResult.getMessage());
                throw BusinessProcessException.fromStep("销售订单完成确认失败: " + finalResult.getMessage(),
                                "POLLING_INCOMPLETE", true, getStepName(), request.getTargetCompanyCode(), request.getBusinessId());
            }

            log.info("销售订单完成确认成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        } catch (BusinessProcessException e) {
            // 重新抛出业务流程异常
            throw e;
        } catch (Exception e) {
            log.error("等待销售订单完成异常，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId, e);
            throw BusinessProcessException.fromStep("等待销售订单完成系统异常",
                            "POLLING_ERROR", true, getStepName(), request.getTargetCompanyCode(), request.getBusinessId());
        }
    }

    @Override
    public void updateFlowOrderInfo(FlowOrderInfoUpdateRequest updateRequest,
                                    CompanyBusinessRequest request) {
        // 采购单创建步骤的状态更新逻辑
        updateRequest.setFlowOrderInfoType(1); // 销售类型
        updateRequest.setFlowOrderInfoNo("");
        updateRequest.setOrderStatus(2); // 审核

        log.debug("销售订单步骤更新汇总信息，业务编号: {}, flowNodeId: {}",
                updateRequest.getFlowOrderInfoNo(), updateRequest.getFlowNodeId());
    }
}
