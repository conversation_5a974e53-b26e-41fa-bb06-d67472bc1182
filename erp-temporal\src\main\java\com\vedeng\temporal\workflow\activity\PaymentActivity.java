package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * 付款Activity接口
 * 
 * 设计理念：
 * - 将PaymentFlow中的业务方法拆分为独立的Activity方法
 * - 支持细粒度重试，失败时只重试失败的步骤
 * - 保持架构简单，统一包结构便于管理
 * 
 * 核心功能：
 * - 付款申请：创建付款申请并返回付款单ID
 * - 付款查询：查询付款状态，用于流程控制
 * 
 * 重试策略：
 * - 创建操作：重试3次，适合网络异常恢复
 * - 查询操作：重试5次，适合临时性查询失败
 * 
 * API接口（仅限以下3个）：
 * - 付款申请：/api/v1/pay/apply.do
 * - 付款查询：/api/v1/pay/query.do
 * - 查询结款：/api/v1/sale/query.do
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-21
 */
@ActivityInterface
public interface PaymentActivity {
    
    /**
     * 创建付款申请
     * 
     * 功能说明：
     * - 根据业务请求创建付款申请
     * - 返回生成的付款单ID
     * - 支持幂等性，重试时不会重复创建
     * 
     * 业务流程：
     * 1. 数据准备：组装付款数据、计算付款金额
     * 2. 付款申请：创建付款申请
     * 3. 付款确认：确认付款结果
     * 
     * 付款前提条件：
     * - 当前公司销售单已经结款
     * - 上游采购单已经付款
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求，包含付款申请所需的数据
     * @return 创建结果，成功时包含付款单ID
     */
    @ActivityMethod
    CompanyBusinessResponse createPayment(CompanyBusinessRequest request);
    

}
