package com.vedeng.api.standard.adapter.receiptInvoice;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceQueryDetailRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceQueryRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceRequest;
import com.vedeng.api.standard.adapter.receiptInvoice.dto.ReceiptInvoiceResponse;
import com.vedeng.api.standard.converter.ResponseConfig;
import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.ReceiptInvoiceApproveRule;
import com.vedeng.api.standard.validation.rules.ReceiptInvoiceEnableRule;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceGoodsResultDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.service.InvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 收票申请适配器
 */
@Component("receiptInvoiceServiceAdapter")
public class ReceiptInvoiceServiceAdapter extends AbstractServiceAdapter {
    
    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("queryInvoiceDetail", this::executeQueryInvoiceDetailOperation);
        registerThrowingHandler("approve", this::executeApproveOperation);
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","queryInvoiceDetail"};
    }


    @Override
    public String getModuleName() {
        return "receiptInvoice";
    }
    
    @Autowired
    private InvoiceApiService invoiceApiService;
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    
    @Autowired
    private InvoiceService invoiceService;

    @Override
    public void preProcess(String action, ApiRequest apiRequest) {
        super.preProcess(action, apiRequest);
    }

    @Override
    public Object postProcess(String action, ApiRequest request, Object result) {
        return super.postProcess(action, request, result);
    }
    
    /**
     * 执行采购录票操作
     */
    private Object executeCreateOperation(ApiRequest request) {
        ResponseMappingConfig responseConfig = ResponseConfig.create("采购录票成功");

        ReceiptInvoiceRequest receiptInvoiceRequest = new ReceiptInvoiceRequest();
        BeanUtil.fillBeanWithMap(request.getData(), receiptInvoiceRequest, true);

        try {
            logger.info("开始执行采购录票: requestId={}", request.getRequestId());
            
            return businessTemplate.<ReceiptInvoiceRequest, ReceiptInvoiceResponse>executeCreate(request)
                    .requestType(ReceiptInvoiceRequest.class)
                    .responseType(ReceiptInvoiceResponse.class)
                    .validationRules(ReceiptInvoiceEnableRule.class)
                    .controller("invoiceController", "saveBuyOrderInvoiceNew")
                    .withIdempotencyHandling("RECEIPT_INVOICE")
                    .withHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE, Invoice.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.SAVE_INVOICE_TYPE, Integer.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.RELATED_ID_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.DETAIL_GOODS_ID_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_NUM_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_PRICE_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.GOODS_TYPE_ARR, String.class),
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE_TOTAL_AMOUNT_ARR, String.class)
                    )
                    .responseConfig(responseConfig)
                    
                    .execute();
        } catch (Exception e) {
            logger.error("执行采购录票操作失败: requestId={}", request.getRequestId(), e);
            throw new RuntimeException("采购录票:失败: " + e.getMessage(), e);
        }
    }
    

    private Object executeQueryOperation(ApiRequest request) throws Exception {
        ReceiptInvoiceQueryRequest invoiceRequest = new ReceiptInvoiceQueryRequest();
        BeanUtil.fillBeanWithMap(request.getData(), invoiceRequest, true);
        return businessTemplate.<ReceiptInvoiceQueryRequest, InvoiceDto>executeQuery(request)
                .requestType(ReceiptInvoiceQueryRequest.class)
                .responseType(InvoiceDto.class)
                .controller("invoiceServiceImpl", "queryReceiptInvoiceRecord")
                .withoutHttpParameters(
                        ParameterConfig.integer(invoiceRequest.getBuyOrderId()),
                        ParameterConfig.string(invoiceRequest.getInvoiceNo())
                ).execute();
    }

    private Object executeQueryInvoiceDetailOperation(ApiRequest request) throws Exception{
        ReceiptInvoiceQueryDetailRequest invoiceRequest = new ReceiptInvoiceQueryDetailRequest();
        BeanUtil.fillBeanWithMap(request.getData(), invoiceRequest, true);
        return businessTemplate.<ReceiptInvoiceQueryDetailRequest, InvoiceGoodsResultDto>executeQuery(request)
                .requestType(ReceiptInvoiceQueryDetailRequest.class)
                .responseType(InvoiceGoodsResultDto.class)
                .controller("invoiceServiceImpl", "queryInvoiceGoodsBySaleorderNo")
                .withoutHttpParameters(
                        ParameterConfig.string(invoiceRequest.getSaleOrderNo())
                ).execute();
    }

    private Object executeApproveOperation(ApiRequest request) {
        ReceiptInvoiceRequest receiptInvoiceRequest = new ReceiptInvoiceRequest();
        BeanUtil.fillBeanWithMap(request.getData(), receiptInvoiceRequest, true);

        try {
            logger.info("开始执行收票审核: requestId={}", request.getRequestId());
            return businessTemplate.<ReceiptInvoiceRequest, ReceiptInvoiceResponse>executeCreate(request)
                    .requestType(ReceiptInvoiceRequest.class)
                    .responseType(ReceiptInvoiceResponse.class)
                    .validationRules(ReceiptInvoiceApproveRule.class)
                    .controller("invoiceService", "saveInvoiceAudit")
                    .withoutHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.INVOICE, Invoice.class)
                    )
                    .execute();
        } catch (Exception e) {
            logger.error("执行收票审核操作失败: requestId={}", request.getRequestId(), e);
            throw new RuntimeException("收票审核:失败: " + e.getMessage(), e);
        }
    }

}
