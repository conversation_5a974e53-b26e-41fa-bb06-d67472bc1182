package com.vedeng.temporal.notification;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 通知去重器（简化版）
 * 防止短时间内发送重复通知，统一处理所有类型错误
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0
 * @since 2025-01-25
 */
@Component
@Slf4j
public class NotificationDeduplicator {

    private final Map<String, LocalDateTime> sentMessages = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    
    @Autowired
    private NotificationConfig config;
    
    @PostConstruct
    public void init() {
        // 每小时清理一次过期记录
        cleanupExecutor.scheduleAtFixedRate(this::cleanExpiredRecords, 1, 1, TimeUnit.HOURS);
        log.info("通知去重器初始化完成，启动定期清理任务");
    }
    
    @PreDestroy
    public void destroy() {
        cleanupExecutor.shutdown();
        log.info("通知去重器已关闭");
    }
    
    /**
     * 检查是否应该发送通知
     * 
     * @param context 通知上下文
     * @return 是否应该发送
     */
    public boolean shouldSend(NotificationContext context) {
        if (!config.isDeduplicationEnabled()) {
            return true;
        }
        
        String key = generateDeduplicationKey(context);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastSent = sentMessages.get(key);
        
        if (lastSent == null) {
            sentMessages.put(key, now);
            log.debug("首次发送通知，key: {}", key);
            return true;
        }
        
        int windowMinutes = config.getDeduplicationWindow();
        boolean shouldSend = lastSent.plusMinutes(windowMinutes).isBefore(now);
        
        if (shouldSend) {
            sentMessages.put(key, now);
            log.debug("超过去重窗口，允许发送通知，key: {}", key);
        } else {
            log.debug("在去重窗口内，跳过通知发送，key: {}", key);
        }
        
        return shouldSend;
    }
    
    /**
     * 生成去重Key
     * 基于业务ID、操作名称、错误信息生成唯一标识
     */
    private String generateDeduplicationKey(NotificationContext context) {
        StringBuilder keyBuilder = new StringBuilder();
        
        if (context.getBusinessId() != null) {
            keyBuilder.append(context.getBusinessId()).append(":");
        }
        
        if (context.getOperationName() != null) {
            keyBuilder.append(context.getOperationName()).append(":");
        }
        
        // 添加错误信息的简化哈希
        if (context.getErrorCode() != null || context.getExceptionType() != null) {
            String errorInfo = (context.getErrorCode() != null ? context.getErrorCode() : "") + 
                              (context.getExceptionType() != null ? context.getExceptionType() : "");
            keyBuilder.append(errorInfo.hashCode());
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 清理过期记录
     */
    public void cleanExpiredRecords() {
        LocalDateTime now = LocalDateTime.now();
        int windowMinutes = config.getDeduplicationWindow();
        LocalDateTime expireTime = now.minusMinutes(windowMinutes * 2); // 保留2倍窗口时间的记录
        
        int removedCount = 0;
        for (Map.Entry<String, LocalDateTime> entry : sentMessages.entrySet()) {
            if (entry.getValue().isBefore(expireTime)) {
                sentMessages.remove(entry.getKey());
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            log.debug("清理过期去重记录 {} 条，当前记录数: {}", removedCount, sentMessages.size());
        }
    }
    
    /**
     * 获取当前去重记录数量（用于监控）
     */
    public int getRecordCount() {
        return sentMessages.size();
    }
    
    /**
     * 清空所有去重记录（用于测试）
     */
    public void clearAll() {
        sentMessages.clear();
        log.info("已清空所有去重记录");
    }
}
