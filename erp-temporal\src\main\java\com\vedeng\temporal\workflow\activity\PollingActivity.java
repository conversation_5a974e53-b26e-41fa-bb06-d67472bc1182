package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CustomPollingRequest;
import com.vedeng.temporal.domain.dto.PollingRequest;
import com.vedeng.temporal.domain.result.PollingResult;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.Map;

/**
 * 状态轮询活动接口（重构版）
 * 定义业务状态轮询相关的活动方法
 * 
 * 重构说明：
 * - 移除 BusinessStatus 枚举依赖
 * - 使用 PollingResult<T> 作为统一返回类型
 * - 支持携带业务数据的轮询结果
 * - 基于 BusinessCompletionChecker 进行完成判断
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (重构版)
 * @since 2025-01-22
 */
@ActivityInterface
public interface PollingActivity {
    
    /**
     * 轮询直到业务完成
     * 使用传统轮询机制，支持长期等待和系统重启恢复
     * 基于 BusinessCompletionChecker 进行完成判断
     * 
     * @param request 状态查询请求对象
     * @return 轮询结果，包含完成状态和业务数据
     * @throws RuntimeException 如果轮询超时或遇到不可恢复的错误
     */
    @ActivityMethod(name = "pollUntilCompleted")
    PollingResult<Map<String, Object>> pollUntilCompleted(PollingRequest request);
    
    /**
     * 检查单次业务状态
     * 执行一次状态查询，不进行轮询
     * 基于 BusinessCompletionChecker 进行完成判断
     *
     * @param request 状态查询请求对象
     * @return 检查结果，包含完成状态和业务数据
     * @throws RuntimeException 如果状态查询失败
     */
    @ActivityMethod(name = "checkBusinessStatus")
    PollingResult<Map<String, Object>> checkBusinessStatus(PollingRequest request);
    
    /**
     * 自定义轮询直到完成
     * 支持函数式查询逻辑，适用于复杂业务场景
     * 
     * @param request 自定义轮询请求对象
     * @return 轮询结果，包含完成状态和业务数据
     * @throws RuntimeException 如果轮询超时或遇到不可恢复的错误
     */
    @ActivityMethod(name = "pollWithCustomQuery")
    PollingResult<Map<String, Object>> pollWithCustomQuery(CustomPollingRequest request);
    
    /**
     * 泛型轮询方法
     * 支持指定返回数据类型的轮询操作
     * 
     * @param request 轮询请求对象
     * @param dataType 期望的数据类型Class
     * @param <T> 数据类型
     * @return 指定类型的轮询结果
     * @throws RuntimeException 如果轮询失败或类型转换失败
     */
    @ActivityMethod(name = "pollWithDataType")
    <T> PollingResult<T> pollWithDataType(PollingRequest request, Class<T> dataType);
    

}
