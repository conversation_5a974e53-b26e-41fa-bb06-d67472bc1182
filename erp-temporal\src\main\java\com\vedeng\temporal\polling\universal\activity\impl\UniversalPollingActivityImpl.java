package com.vedeng.temporal.polling.universal.activity.impl;

import com.vedeng.temporal.polling.universal.activity.UniversalPollingActivity;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.executor.LocalQueryExecutor;
import com.vedeng.temporal.polling.universal.executor.RemoteQueryExecutor;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.validation.BusinessCompletionChecker;
import com.vedeng.temporal.validation.CompletionCheckConfigParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一轮询Activity实现类
 * 
 * 实现统一轮询Activity接口，负责执行单次查询操作并返回结果。
 * 根据请求中的数据源类型，选择相应的查询执行器来处理具体的查询逻辑。
 * 
 * 主要功能：
 * - 路由到正确的查询执行器（远程API或本地数据库）
 * - 执行查询并获取数据
 * - 使用条件检查器判断是否满足完成条件
 * - 构建完整的查询结果
 * - 提供健康检查功能
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@Slf4j
@Component
public class UniversalPollingActivityImpl implements UniversalPollingActivity {
    
    @Autowired
    private RemoteQueryExecutor<Map<String, Object>> remoteQueryExecutor;
    
    @Autowired
    private LocalQueryExecutor<Map<String, Object>> localQueryExecutor;
    
    @Override
    public UniversalPollingResult<Map<String, Object>> executeQuery(
            UniversalPollingRequest request) {
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            log.debug("开始执行统一查询，请求: {}", request.formatDescription());
            
            // 验证请求参数
            request.validate();
            
            // 执行查询
            UniversalPollingResult<Map<String, Object>> result = performQuery(request, startTime);
            
            log.debug("统一查询执行完成，请求: {}, 结果: {}", 
                    request.createRequestKey(), result.formatDescription());
            
            return result;
            
        } catch (IllegalArgumentException e) {
            log.error("统一查询参数无效，请求: {}, 错误: {}", request.createRequestKey(), e.getMessage());
            return UniversalPollingResult.failure(
                    "查询参数无效: " + e.getMessage());
            
        } catch (Exception e) {
            log.error("统一查询执行异常，请求: {}, 错误: {}", request.createRequestKey(), e.getMessage(), e);
            return UniversalPollingResult.failure(
                    "查询执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行具体的查询操作
     * 
     * @param request 查询请求
     * @param startTime 开始时间
     * @return 查询结果
     */
    private UniversalPollingResult<Map<String, Object>> performQuery(
            UniversalPollingRequest request, LocalDateTime startTime) throws Exception {
        
        // 根据数据源类型选择执行器
        Map<String, Object> context = buildQueryContext(request);
        Map<String, Object> queryData = executeQueryByDataSource(request.getDataSourceType(), context);
        
        // 完成条件检查
        BusinessCompletionChecker businessChecker = CompletionCheckConfigParser.parseConfig(request.getCompletionCheckConfig());
        boolean isCompleted = businessChecker.isCompleted(queryData, null);
        
        // 构建结果
        if (isCompleted) {
            log.debug("查询条件满足，已完成，请求: {}", request.createRequestKey());
            return UniversalPollingResult.success(queryData, "查询完成，条件满足");
        } else {
            log.debug("查询条件未满足，未完成，请求: {}", request.createRequestKey());
            return UniversalPollingResult.failure("查询成功，但条件未满足");
        }
    }
    
    /**
     * 根据数据源类型执行查询
     * 
     * @param dataSourceType 数据源类型
     * @param context 查询上下文
     * @return 查询数据
     */
    private Map<String, Object> executeQueryByDataSource(DataSourceType dataSourceType, 
                                                        Map<String, Object> context) throws Exception {
        
        switch (dataSourceType) {
            case REMOTE_API:
                log.debug("使用远程API执行器执行查询");
                return remoteQueryExecutor.executeRemoteQuery(context);
                
            case LOCAL_DATABASE:
                log.debug("使用本地数据库执行器执行查询");
                return localQueryExecutor.executeLocalQuery(context);
                
            default:
                throw new IllegalArgumentException("不支持的数据源类型: " + dataSourceType);
        }
    }
    
    /**
     * 构建查询上下文
     * 
     * @param request 查询请求
     * @return 查询上下文Map
     */
    private Map<String, Object> buildQueryContext(UniversalPollingRequest request) {
        Map<String, Object> context = new HashMap<>();
        
        // 添加基础信息
        context.put("businessId", request.getBusinessId());
        context.put("companyCode", request.getCompanyCode());
        context.put("requestKey", request.createRequestKey());
        context.put("dataSourceType", request.getDataSourceType());
        
        // 添加数据源配置
        switch (request.getDataSourceType()) {
            case REMOTE_API:
                context.put("apiPath", request.getApiPath());
                context.put("apiParameters", request.getApiParameters());
                break;
            case LOCAL_DATABASE:
                // 构建DatabaseConfig对象给DatabaseQueryExecutor使用
                com.vedeng.temporal.polling.universal.config.DatabaseConfig databaseConfig = 
                    com.vedeng.temporal.polling.universal.config.DatabaseConfig.create(
                        request.getQueryType(),
                        request.getBusinessId(),
                        request.getQueryParameters(),
                        request.getCompanyCode()
                    );
                context.put("databaseConfig", databaseConfig);
                break;
        }
        
        return context;
    }
    
}