package com.vedeng.api.standard.adapter.pay;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.api.standard.adapter.pay.dto.*;
import com.vedeng.api.standard.converter.ResponseConfig;
import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.finance.model.PayApply;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 付款申请适配器
 */
@Component("payServiceAdapter")
public class PayServiceAdapter extends AbstractServiceAdapter {
    
    @Autowired
    private BusinessTemplate businessTemplate;
    
    @Value("${erp_short_name}")
    private String erpShortName;

    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("apply", this::executeApplyOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("check", this::executeCheckSaleOrderOperation);
        
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","check"};
    }


    @Override
    public String getModuleName() {
        return "pay";
    }

    @Override
    public void preProcess(String action, ApiRequest request) {
        super.preProcess(action, request);
    }

    @Override
    public Object postProcess(String action, ApiRequest request, Object result) {
        return super.postProcess(action, request, result);
    }
    
    /**
     * 执行申请付款操作
     * 调用 BuyorderController 的 saveApplyPayment 方法
     */
    private Object executeApplyOperation(ApiRequest request) {
        ResponseMappingConfig responseConfig = ResponseConfig.create("付款申请成功");

        PayApplyRequest payApplyRequest = new PayApplyRequest();
        BeanUtil.fillBeanWithMap(request.getData(), payApplyRequest, true);

        PayApplyAdapterDto payApply = convertToPayApply(payApplyRequest);
        try {
            logger.info("开始执行付款申请操作: requestId={}", request.getRequestId());
            
            return businessTemplate.<PayApplyRequest, PayApplyResponse>executeCreate(request)
                    .requestType(PayApplyRequest.class)
                    .responseType(PayApplyResponse.class)
                    .controller("buyorderController", "saveApplyPayment")
                    .withIdempotencyHandling("PAY_APPLY")
                    .withHttpParameters(
                            ParameterConfig.requestParam("buyorderGoodsIdArr",payApply.getGoodsIdList()),
                            ParameterConfig.of(PayApply.class, payApply.getPayApply()),
                            ParameterConfig.string(payApply.getPriceArr()),
                            ParameterConfig.string(payApply.getNumArr()),
                            ParameterConfig.string(payApply.getTotalAmountArr()),
                            ParameterConfig.string(""),
                            ParameterConfig.string(""),
                            ParameterConfig.string(""),
                            ParameterConfig.integer(payApply.getPayApplyType())
                    )
                    .responseConfig(responseConfig)
                    .execute();
        } catch (Exception e) {
            logger.error("执行付款申请操作失败: requestId={}", request.getRequestId(), e);
            throw new RuntimeException("付款申请失败: " + e.getMessage(), e);
        }
    }

    @Autowired
    private BuyorderApiService buyorderApiService;
    @Autowired
    private TraderFinanceApiService traderFinanceApiService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;
    
    private PayApplyAdapterDto convertToPayApply(PayApplyRequest payApplyRequest) {
        String buyOrderNo = payApplyRequest.getBuyOrderNo();
        BuyOrderApiDto buyorder = buyorderApiService.getBuyorderByBuyorderNo(buyOrderNo);
        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByShortName(erpShortName);
//        TraderFinanceDto traderFinanceDto = traderFinanceApiService.selectByTraderIdAndTraderType(buyorder.getTraderId(), 2);

        PayApply payApply = new PayApply();
        payApply.setAmount(buyorder.getTotalAmount());
        payApply.setBuyorder_amount(buyorder.getTotalAmount());
        payApply.setIsUseBalance(2); // 不使用
        payApply.setTraderSubject(1); // 对公
        payApply.setTraderMode(521); // 银行
        payApply.setBankRemark(buyorder.getBuyorderNo());
        payApply.setBank(baseCompanyInfoDto.getBankName());
        payApply.setBankCode(baseCompanyInfoDto.getBaseCompanyInfoDetailDto().getBankCode());
        payApply.setBankAccount(baseCompanyInfoDto.getBankAccount());
        payApply.setTraderName(buyorder.getTraderName());
        payApply.setTraderId(buyorder.getTraderId());
        payApply.setRelatedId(buyorder.getBuyorderId());
        payApply.setTraderSupplierId(buyorder.getTraderSupplierId());
        List<BuyorderGoodsApiDto> goodsList = buyorder.getBuyorderGoodsApiDtos();


        PayApplyAdapterDto payApplyAdapterDto = new PayApplyAdapterDto();
        payApplyAdapterDto.setPayApply(payApply);
        // 转成json
        payApplyAdapterDto.setGoodsIdList(goodsList.stream().map(BuyorderGoodsApiDto::getGoodsId).collect(Collectors.toList()).toString());
        payApplyAdapterDto.setPriceArr(goodsList.stream().map(BuyorderGoodsApiDto::getPrice).collect(Collectors.toList()).toString());
        payApplyAdapterDto.setNumArr(goodsList.stream().map(BuyorderGoodsApiDto::getNum).collect(Collectors.toList()).toString());
        payApplyAdapterDto.setTotalAmountArr(goodsList.stream().map(e-> e.getPrice().multiply(new BigDecimal(e.getNum()))).collect(Collectors.toList()).toString());
        payApplyAdapterDto.setPayApplyType(0); // 采购付款
        return payApplyAdapterDto;
    }

    private Object executeQueryOperation(ApiRequest request) throws Exception {
        PayApplyRequest payApplyRequest = new PayApplyRequest();
        BeanUtil.fillBeanWithMap(request.getData(), payApplyRequest, true);

        BuyOrderApiDto buyorder = buyorderApiService.getBuyorderByBuyorderNo(payApplyRequest.getBuyOrderNo());
        
        return businessTemplate.<PayApplyRequest, PayApplyDto>executeQuery(request)
                .requestType(PayApplyRequest.class)
                .responseType(PayApplyDto.class)
                .controller("payApplyApiServiceImpl", "getByPayTypeAndRelatedIdLast")
                .withoutHttpParameters(
                        ParameterConfig.integer(517), //采购付款
                        ParameterConfig.integer(buyorder.getBuyorderId())
                ).execute();
    }

    /**
     * 执行检查销售订单状态操作
     * 调用 OrderInfoSyncService 的 calculatePaymentStatus 方法
     */
    private Object executeCheckSaleOrderOperation(ApiRequest request) throws Exception {
        PayApplyCheckRequest checkRequest = new PayApplyCheckRequest();
        BeanUtil.fillBeanWithMap(request.getData(), checkRequest, true);

        // 根据采购订单号获取销售订单ID
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderNo(checkRequest.getSaleOrderNo());
        
        return businessTemplate.<PayApplyCheckRequest, PayApplyCheckResponse>executeQuery(request)
                .requestType(PayApplyCheckRequest.class)
                .responseType(PayApplyCheckResponse.class)
                .controller("orderInfoSyncService", "calculatePaymentStatus")
                .withoutHttpParameters(
                        ParameterConfig.integer(saleorderInfoDto.getSaleorderId())
                ).execute();
    }

}
