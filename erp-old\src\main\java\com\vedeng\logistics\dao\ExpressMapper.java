package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.vo.BatchExpressVo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.ExpressOnlineReceiptVo;
import com.vedeng.order.model.vo.ExpressSkuDataVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;


@Named("expressMapper")
public interface ExpressMapper {
	int deleteByPrimaryKey(Integer expressId);
	int insertSelective(Express record);
    /**
     * 
     * <b>Description:</b><br>  查询所有的出库订单物流信息
     * @param express
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年8月30日 下午5:09:00
     */
	List<Express> getExpressInfoListForSaleorder( );
	List<Express> getExpressInfoListForBuyorder( );


	 Express  getExpressInfoBylogisticsNo(@Param("logisticsNo")String logisticsNo,@Param("logisticsId")Integer logisticsId);

	/**
	* @Title: getLendOutExpressInfo
	* @Description: TODO(查看外接单物流信息)
	* @param @param express
	* @return List<Express>    返回类型
	* <AUTHOR>
	* @throws
	* @date 2019年8月29日
	*/
	List<Express> getLendOutExpressInfo(Express express);
	
	
	/** 
	* @Description: 发货提醒参数接口 
	* @Param: [express] 
	* @return: java.util.List<com.vedeng.logistics.model.ShipmentToRemind> 
	* @Author: addis
	* @Date: 2019/9/25 
	*/ 
/*	List<ShipmentToRemind> shipmentToRemind(Express express);*/

    
    /**
    * @Description: 根据快递单号查询商品的数量和订货号
    * @Param: 
    * @return: 
    * @Author: addis
    * @Date: 2019/11/9
    */ 
	List<LogisticsOrderGoodsData> selectExpressGood(@Param(value = "expressId") Integer expressId);

	Integer batchInsert(List<LogisticsDetail> logisticsDetailList);
	/**
	*获取当前订单快递详情
	* @Author:strange
	* @Date:19:45 2019-12-30
	*/
	List<ExpressDetail> getExpressDetailList(Saleorder saleorder);
	//20.19-12-31
    List<Integer> getExpressIds();
	/**
	 * 获取当前快递下某商品数量
	 * @Author:strange
	 * @Date:15:24 2020-01-06
	 */
    ExpressDetail getExpressDetailNumByExpressId(ExpressDetail expressDetail);
	/**
	 *获取当前快递单商品详情
	 * @Author:strange
	 * @Date:08:55 2020-01-07
	 */
	List<ExpressDetail> getExpressDetailByExpressId(Integer expressId);

	/**
	 * 查询是否第一次物流
	 */
	List<Express> getFirst(Integer traId);
	//改变是否开据发票状态
    int changeIsinvoicing(Integer invoiceApplyId);
	//改变是否开据发票状态
    int updateIsinvoicing(Integer expressId);

	/**
	 * 更新快递单的签收状态
	 * @param express
	 */
	void updateExpressArrivalStatusById(Express express);
	int updateByPrimaryKeySelective(Express express);
	/**
	 * 获取已经签收的商品数量
	 * @return
	 */
	Integer getSaleorderGoodCountHasReceived(Integer expressDetailId);

	/**
	 * 获取已经发送的商品数量
	 * @return
	 */
	Integer getSaleorderGoodsNumOfExpress(Integer saleorderGoodsId);
	//改变是否开据发票状态
    int updateIsinvoicingNo(Integer expressId);

	Express getSEGoodsNum(Express express);
	/**
	*获取未删除快递单id
	* @Author:strange
	* @Date:14:58 2020-02-10
	*/
    Integer getExpressIdByWlogId(Integer wlogId);
	/**
	 *
	 * <b>Description:</b><br> 根据快递单号查询快递单详情
	 * @param express
	 * @return
	 * @Note
	 * <b>Author:</b> scott
	 * <br><b>Date:</b> 2018年1月22日 下午2:59:52
	 */
    List<ExpressDetail> getExpressDetailsList(Express express);

	List<ExpressDetail> getExpressDetailListByBuyorderId(Integer buyorderId);

	/**
	 * 以保存的wms单号快递单
	 * @param wmsOrderNo  erpNo
	 * @param logisticsNo
     * @return
	 */
    Express getExpressInfoByWmsNo(@Param("wmsOrderNo") String wmsOrderNo, @Param("orderId") Integer orderId,@Param("logisticsNo") String logisticsNo);

    Express getExpressInfoByLogNoAndComments(Express express);

   List< Express> getExpressInfoByLogAndComments(Express search);

    Express getExpressInfoById(Integer expressId);

	List<ExpressDetail> getExpressDetailsListByBuyorderGoodsId(BuyorderGoods buyorderGoods);

	/**
	 * 获取快递单商品总额
	 *
	 * @param expressId
	 * @return
	 */
	BigDecimal getGoodsAmountByExpressId(Integer expressId);

	/**
	 *
	 * @param express
	 * @return
	 */
	List<Express> selectAmountByExpressId(Express express);

	/**
	 * 检索本次快递之前的包裹信息ID集合
	 *
	 * @param orderId
	 * @param expressId
	 * @return
	 */
	List<Integer> getExpressIdsThisDeliveryBefore(@Param("orderId") Integer orderId, @Param("expressId") Integer expressId);

	/**
	 * 根据条件返回快递单信息
	 *
	 * @param express
	 * @return
	 */
	List<Express> getExpressInfo(Express express);

	/**
	 * 根据条件返回确认单快递单信息 销售订单
	 *
	 * @param express
	 * @return
	 */
	List<Express> getExpressInfoConfirmation(Express express);

	/**
	 * <b>Description:</b><br> 获取快递主表
	 * @param expressInfo
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年1月16日 下午4:46:01
	 */
	Express getExpressById(Express expressInfo);

	/**
	 * 逻辑删除
	 */
	int logicalDeleteExpress(@Param("expressId") Integer expressId);

    Integer getBuyorderByExpressId(Integer expressId);

	List<ExpressDetail> getEnableExpressDetailListByBuyorderId(Integer buyorderId);

    /**
     * 根据buyorderId查询对应的物流信息中商品数量之和
     */
    Integer getTotalNumInExpressDetailByBuyorderId(@Param("buyorderId")Integer buyorderId);

	/**
	 * 更新采购关联物流到货状态
	 * @param buyOrderGoodsList
	 */
	void updateArrivalStatusByRelatedKey(@Param("list") List<BuyorderGoods> buyOrderGoodsList);

    Express selectExpressByPrimaryKey(@Param("expressId") Integer expressId);

	BigDecimal getAllGoodsAmountByExpressId(Integer expressId);

    Buyorder getBuyorderInfoByExpressId(Integer expressId);

	Integer getPurchaseDeliveryDirectBatchDetail(Integer expressId);

	/**
	 * 订单号获取包裹信息
	 * @param orderNo
	 * @return
	 */
	List<Express> getExpressListByOrderNo(String orderNo);

	/**
	 * 主键更新在线签收信息
	 * @param onlineReceiptId
	 * @param expressIdList
	 */
	void updateOnLineReceiptIdByExpressIds(@Param("onlineReceiptId") Integer onlineReceiptId,@Param("expressIdList") List<Integer> expressIdList);

	/**
	 * 订单ID获取客户在线签收消息
	 * @param orderId
	 * @return
	 */
	List<ExpressOnlineReceiptVo> getExpressOnlineReceiptListByOrderId(Integer orderId);


	List<ExpressOnlineReceiptVo> getExpressOnlineReceiptListByOnlineReceiptId(Integer onlineReceiptId);

	List<ExpressSkuDataVo> getExpressSkuDataByExpressId(Integer expressId);

	/**
	 * 主键ID获取客户在线签收消息
	 * @param Id
	 * @return
	 */
	ExpressOnlineReceiptVo getExpressOnlineReceiptById(Integer Id);


	/**
	 * 销售订单类型根据expressId去查询订单中收货联系人手机
	 * @Param expressId
	 */
	List<String> getPhoneByBusinessTypeSaleOrder(@Param("expressId") Integer expressId);

	/**
	 * 采购订单类型根据expressId去查询订单中收货联系人手机
	 * @Param expressId
	 */
	List<String> getPhoneByBusinessTypeBuyOrder(@Param("expressId") Integer expressId);

	/**
	 * 发票寄送类型根据expressId去查询订单中收票联系人手机
	 * @Param expressId
	 */
	List<String> getPhoneByBusinessTypeInvoice(@Param("expressId") Integer expressId);

	/**
	 * 根据物流公司id和物流单号修改express签收状态
	 * @param expressIdList,logisticsNo
	 */
	int updateExpressArrivalStatusByLogisticsNo(@Param("expressIdList")List<Integer> expressIdList,@Param("logisticsNo") String logisticsNo);
	/**
	 * 订单ID获取快递信息
	 *
	 * @param orderIds
	 * @return
	 */
	List<Express> getExpressListByOrderIds(@Param("orderIds") List<Integer> orderIds);

	/**
	 * 根据物流单号查询express，只取1条
	 */
	Express getExpressByLogisticsNo(@Param("logisticsNo")String logisticsNo);

	/**
	 * 取消采购单限制
	 * @param logisticsNo
	 * @return
	 */
	Express getExpressByLogisticsNoNoLimit(@Param("logisticsNo")String logisticsNo);

    List<Express> getExpressInfoNew(Express express);

	List<Express> getBuyExpressList(Express express);

	/**
	 * 根据批次号获取所有快递
	 */
	List<Express> getExpressListByBatchNo(@Param("batchNo")String batchNo);

	List<BatchExpressVo> getBatchExpressByIds(@Param("expressIds") List<Integer> expressIds);

	/**
	 * 根据express主键id批量查询批次编号
	 * @param expressIds
	 * @return
	 */
	List<String> selectBatchNosByExpressIds(@Param("expressIds") List<Integer> expressIds);

	/**
	 * 判断当前批次下的所有快递是否都已经在线确认
	 * @param batchNo
	 * @return
	 */
	Integer verifyAllOnlineConfirmation(@Param("batchNo") String batchNo);

    List<Express> selectFirstEnableReceiveList(@Param("logisticsNo") String logisticsNo, @Param("limitSize")Integer limitSize);

	Integer updateEnableReceiveById(Integer expressId);

	List<Express> selectExpressListByBuyOrderId(Integer buyorderId);

	List<Express> selectContinueEnableReceiveList(@Param("lastExpressID") Integer lastExpressID, @Param("limitSize") Integer limitSize);


	List<Express> findByWmsOrderNo(@Param("wmsOrderNo")String wmsOrderNo);


	List<Express> getExpresslistPage(@Param("express") Express express,
									 @Param("beginTime") Long beginTime,
									 @Param("endTime") Long endTime);

	List<SyncExpressDto> getSyncExpressListForSaleOrder(@Param("saleOrderNo") String saleOrderNo);

	List<SyncExpressDto> getSyncExpressListForSaleOrderForAutoCreateConfirmOrder(@Param("saleOrderNo") String saleOrderNo);

	List<SyncExpressDto> getSyncExpressListForBuyOrder(@Param("buyOrderNo") String buyOrderNo);

    Integer getSendedNum(@Param("buyOrderGoodsId") Integer buyOrderGoodsId);

	SyncExpressDetailDto getDetail( @Param("buyorderId") Integer buyorderId, @Param("logisticsNo") String logisticsNo, @Param("sku") String sku);
}
