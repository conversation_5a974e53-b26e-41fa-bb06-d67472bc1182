package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.RegionDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import com.vedeng.erp.trader.service.TraderCustomerErpApiService;
import com.vedeng.temporal.domain.dto.*;
import com.vedeng.temporal.dto.SaleOrderFlowResponseDto;
import com.vedeng.temporal.mapper.FlowNodeForSaleMapper;
import com.vedeng.temporal.mapper.FlowOrderQueryMapper;
import com.vedeng.temporal.mapper.SaleFlowMapper;
import com.vedeng.temporal.workflow.activity.SalesOrderActivity;
import com.vedeng.temporal.workflow.activity.core.UniversalActivityTemplate;
import com.vedeng.temporal.exception.BusinessProcessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售订单 Activity 实现类 - 增强版
 *
 * 架构迁移说明：
 * - 从 SalesOrderFlow 迁移 buildSalesOrderBaseData 核心业务逻辑到 Activity 层
 * - 使用 UniversalBusinessTemplate 处理所有业务逻辑：固定参数、API调用、异常处理、通知发送
 * - 每个方法都是独立的 Activity，由 Temporal 管理重试
 * - 直接使用 CompanyBusinessRequest 传递数据，自动解析 JSON
 * - 集成完整的销售订单数据准备逻辑：公司信息查询、流程节点查询、数据转换
 *
 * 业务流程：
 * 1. createSalesOrder - 创建销售订单，包含完整的数据准备逻辑，返回订单ID
 * 2. submitSalesOrderForApproval - 提交审核，需要订单ID
 * 3. approveSalesOrder - 审核通过，需要订单ID
 * 4. querySalesOrderStatus - 查询状态，用于流程控制
 *
 * 迁移内容：
 * - buildSalesOrderBaseData 方法及其完整业务逻辑
 * - convertSaleFlowToSaleFlowOrder 数据转换逻辑
 * - 相关依赖注入：BaseCompanyInfoApiService、FlowNodeForSaleMapper、SaleFlowMapper 等
 *
 * <AUTHOR> 4.0 sonnet
 * @version 5.0 (架构迁移版本，集成 SalesOrderFlow 核心逻辑)
 * @since 2025-01-21
 */
@Component
@Slf4j
public class SalesOrderActivityImpl implements SalesOrderActivity {

    @Autowired
    private UniversalActivityTemplate universalActivityTemplate;

    // ========== 迁移的依赖注入 - 从 SalesOrderFlow 迁移 ==========

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    @Autowired
    private FlowNodeForSaleMapper flowNodeForSaleMapper;

    @Autowired
    private SaleFlowMapper saleFlowMapper;

    @Autowired
    private FlowOrderQueryMapper flowOrderQueryMapper;

    @Autowired
    private RegionApiService regionApiService;

    @Autowired
    private TraderCustomerErpApiService traderCustomerErpApiService;

    @Autowired
    private TraderAddressApiService traderAddressApiService;

    @Override
    public CompanyBusinessResponse createSalesOrder(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作 - 使用增强的数据准备方法
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("创建销售订单")
                .apiPath("/api/v1/saleorder/create.do")
                .dataPreparer(this::prepareSalesOrderDataEnhanced)  // 使用增强版本
                .resultExtractor(result -> {
                    // 实现与原始 SalesOrderFlow 一致的复杂解析逻辑
                    try {
                        String data = JSON.toJSONString(result.get("data"));
                        if (StringUtils.hasText(data)) {
                           // String tempJson =  JSON.toJSONString(data);
//                            R saleOrderFlowResponseDtoR = JSON.parseObject(data, R.class);
                            SaleOrderFlowResponseDto responseDto = JSON.parseObject(data, SaleOrderFlowResponseDto.class);
                            return String.valueOf(responseDto.getSaleorderId());
                        }
                    } catch (Exception e) {
                        log.warn("解析订单ID失败，尝试简化解析", e);
                        // 降级到简化解析
                        if (result.containsKey("saleorderId")) {
                            return String.valueOf(result.get("saleorderId"));
                        }
                    }
                    return null;
                });

        // 使用异常优先模式
        try {
            return universalActivityTemplate.execute(request, config);
        } catch (BusinessProcessException e) {
            // 转换为Response模式，保持Activity接口兼容性
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        }
    }
    
    @Override
    public CompanyBusinessResponse submitSalesOrderForApproval(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("提交销售订单审核")
                .apiPath("/api/v1/saleorder/submit.do")
                .dataPreparer(this::prepareSubmitData)
                .resultExtractor(result -> "SUCCESS"); // 提交操作不需要返回特定ID

            return universalActivityTemplate.execute(request, config);

    }
    
    @Override
    public CompanyBusinessResponse approveSalesOrder(CompanyBusinessRequest request) {
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(Long.valueOf(request.getBusinessId()));
        request.setUserId(Long.valueOf(flowOrderDto.getCreator()));
        // 配置业务操作
        UniversalActivityTemplate.BusinessOperationConfig config =
            UniversalActivityTemplate.BusinessOperationConfig.create()
                .operationName("审核销售订单")
                .apiPath("/api/v1/saleorder/approve.do")
                .dataPreparer(this::prepareApproveData)
                .resultExtractor(result -> "SUCCESS"); // 审核操作不需要返回特定ID

            return universalActivityTemplate.execute(request, config);

    }
    

    // ========== 私有数据准备方法 ==========

    /**
     * 销售订单数据准备方法 - 简化版
     * 直接构建API调用格式，避免中间对象转换
     */
    private Map<String, Object> prepareSalesOrderDataEnhanced(CompanyBusinessRequest request) {
        log.info("开始准备销售订单数据，业务ID: {}", request.getBusinessId());

        // 解析业务数据
        //Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        String businessId = request.getBusinessId();//(String) businessData.get("businessId");
        String targetCompanyCode = request.getTargetCompanyCode();
        String sourceCompanyCode = request.getSourceCompanyCode();

        // 查询基础数据
        BaseCompanyInfoDto customerCompanyInfo = baseCompanyInfoApiService.selectBaseCompanyByShortName(sourceCompanyCode);
        BaseCompanyInfoDto erpSystemCompanyInfo = baseCompanyInfoApiService.selectBaseCompanyByShortName(targetCompanyCode);
        Long flowOrderId = Long.parseLong(businessId);
        FlowNodeDto flowNodeDto = flowNodeForSaleMapper.selectNodesByFlowOrderId(flowOrderId, erpSystemCompanyInfo.getCompanyName());
        SaleFlowDto saleFlowDto = saleFlowMapper.selectSaleFlowWithSkuAndPrice(flowNodeDto.getFlowOrderId(), flowNodeDto.getNodeLevel());
        FlowOrderDto flowOrderDto = flowOrderQueryMapper.selectByFlowOrderId(saleFlowDto.getFlowOrderId());

        // 直接构建API数据格式
        Map<String, Object> apiData = new HashMap<>();

        // 基本信息
        apiData.put("companyName", customerCompanyInfo.getCompanyName());
        apiData.put("username", flowOrderDto.getCreatorName());
        apiData.put("orderNo", "");
        apiData.put("comments", "");
        apiData.put("remakes", "");
        apiData.put("additionalClause", "");
        apiData.put("logisticsComments", "");
        apiData.put("phone", saleFlowDto.getReceiverPhone());

        // 收货信息
        apiData.put("deliveryUserName", saleFlowDto.getReceiverName());
        apiData.put("deliveryUserPhone", saleFlowDto.getReceiverPhone());
        apiData.put("deliveryUserAddress", saleFlowDto.getReceiverAddress());
        apiData.put("deliveryUserTel", "");
        apiData.put("delayDelivery", 0);
        apiData.put("deliveryType", 482);
        apiData.put("deliveryDirect", "Y");

        // 地区信息
        Integer receiverAddressId = saleFlowDto.getReceiverAddressId();
        //需要根据联系地址表再取一遍
        TraderAddressDto traderAddressDto = traderAddressApiService.findTraderAddressById(receiverAddressId);

        if (traderAddressDto != null) {
            String receiverAddress = regionApiService.getThisRegionToParentRegion(traderAddressDto.getAreaId());
            apiData.put("deliveryUserArea", receiverAddress);

            RegionDto regionDto3 = regionApiService.getRegionDto(traderAddressDto.getAreaId());
            if (regionDto3 != null) {
                apiData.put("deliveryLevel3Id", regionDto3.getRegionId() + "");
                RegionDto regionDto2 = regionApiService.getRegionDto(regionDto3.getParentId());
                if (regionDto2 != null) {
                    apiData.put("deliveryLevel2Id", regionDto2.getRegionId() + "");
                    RegionDto regionDto1 = regionApiService.getRegionDto(regionDto2.getParentId());
                    if (regionDto1 != null) {
                        apiData.put("deliveryLevel1Id", regionDto1.getRegionId() + "");
                    }
                }
            }
        }

        // 发票信息
        apiData.put("invoiceType", saleFlowDto.getInvoiceType());
        apiData.put("isDelayInvoice", (saleFlowDto.getOpenInvoice() != null && saleFlowDto.getOpenInvoice().equals(1)) ? 0 : 1);
        apiData.put("invoiceMethod", null);
        apiData.put("isSendInvoice", 0);
        apiData.put("isPrintout", 2);

        if (customerCompanyInfo.getBaseCompanyInfoDetailDto() != null) {
            apiData.put("invoiceTraderContactName", customerCompanyInfo.getBaseCompanyInfoDetailDto().getTraderContactNameSync());
            apiData.put("invoiceTraderContactMobile", customerCompanyInfo.getBaseCompanyInfoDetailDto().getInvoiceTraderContactMobileSync());
        }

        // 其他信息
        apiData.put("isCoupons", 0);
        apiData.put("totalCouponedAmount", 0);

        // 商品列表和交易者信息
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Map<String, Object>> goodsListData = new ArrayList<>();

        List<SaleFlowSkuDto> saleFlowSkuDtos = saleFlowDto.getSkuList();
        if (saleFlowSkuDtos != null && !saleFlowSkuDtos.isEmpty()) {
            for (SaleFlowSkuDto skuDto : saleFlowSkuDtos) {
                Map<String, Object> goodsData = new HashMap<>();
                goodsData.put("skuNo", skuDto.getSkuNo());
                goodsData.put("productNum", skuDto.getQuantity());
                goodsData.put("jxSalePrice", skuDto.getPrice());

                BigDecimal amount = skuDto.getPrice().multiply(new BigDecimal(skuDto.getQuantity()));
                goodsData.put("skuAmount", amount);
                goodsData.put("isCoupons", 0);
                goodsData.put("haveInstallation", 0);
                goodsData.put("deliveryDirect", 1);
                goodsData.put("deliveryCycle", "3");
                goodsData.put("insideComments", "");
                goodsData.put("goodsComments", "");
                goodsData.put("delilveryDirectComments", "");

                goodsListData.add(goodsData);
                totalAmount = totalAmount.add(amount);
            }
        }

        apiData.put("goodsList", goodsListData);
        apiData.put("totalMoney", totalAmount);

        // 设置交易者ID
        Integer customerTraderId = customerCompanyInfo.getCustomerTraderId();
        if (customerTraderId != null) {
            TraderCustomerErpDto traderCustomerErpDto = traderCustomerErpApiService.queryByTraderCustomerId(customerTraderId);
            if (traderCustomerErpDto != null) {
                apiData.put("traderId", traderCustomerErpDto.getTraderId());
            }
        }

        log.info("销售订单数据准备完成，业务ID: {}, 商品数量: {}, 总金额: {}",
                request.getBusinessId(), goodsListData.size(), totalAmount);

        return apiData;
    }

    /**
     * 准备提交审核数据
     */
    private Map<String, Object> prepareSubmitData(CompanyBusinessRequest request) {
        Map<String, Object> submitData = new HashMap<>();

        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Integer orderId = MapUtil.getInt(businessData,"orderId");        //businessData.get("orderId");
            submitData.put("saleOrderId", orderId);
            submitData.put("orderId", orderId);
        }

        submitData.put("submitTime", System.currentTimeMillis());
        submitData.put("submitReason", "流程自动提交");

        log.debug("准备提交审核数据完成, 业务ID: {}", request.getBusinessId());
        return submitData;
    }

    /**
     * 准备审核数据
     */
    private Map<String, Object> prepareApproveData(CompanyBusinessRequest request) {
        Map<String, Object> approveData = new HashMap<>();

        // 从 businessData 中获取订单ID（businessData 是 JSON 字符串）
        Map<String, Object> businessData = parseBusinessData(request.getBusinessData());
        if (businessData != null && businessData.containsKey("orderId")) {
            Integer orderId = MapUtil.getInt(businessData,"orderId");
            approveData.put("saleOrderId", orderId);
            approveData.put("orderId", orderId);
        }

        approveData.put("approveTime", System.currentTimeMillis());
        approveData.put("approveResult", "APPROVED");
        approveData.put("approveReason", "流程自动审核");

        log.debug("准备审核数据完成, 业务ID: {}", request.getBusinessId());
        return approveData;
    }



    /**
     * 解析业务数据 JSON 字符串
     */
    private Map<String, Object> parseBusinessData(String businessDataJson) {
        if (!StringUtils.hasText(businessDataJson)) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(businessDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("解析业务数据JSON失败: {}", businessDataJson, e);
            return new HashMap<>();
        }
    }


}
