package com.vedeng.temporal.workflow.activity;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.Map;

/**
 * 入库单Activity接口
 * 
 * 设计理念：
 * - 将InventoryReceiptFlow中的业务方法拆分为独立的Activity方法
 * - 支持细粒度重试，失败时只重试失败的步骤
 * - 保持架构简单，统一包结构便于管理
 * 
 * 核心功能：
 * - 入库单创建：执行完整的入库单流程（创建快递 + 查询库存 + 创建同行单）
 * - 快递信息查询：查询快递信息，用于流程控制
 * - 库存记录查询：查询库存记录，用于流程控制
 * - 创建快递：独立的快递创建操作
 * - 查询库存并创建同行单：组合操作，保证数据一致性
 * 
 * 业务流程：
 * 1. 创建快递：调用 /api/v1/express/create，入参是快递查询接口返回对象属性：expressGoodsList
 * 2. 查询库存：调用 /api/v1/peerlist/queryStockRecords，返回对象中的list字段的值作为创建同行单的入参
 * 3. 创建同行单：调用 /api/v1/peerlist/create
 * 
 * 重试策略：
 * - 创建操作：重试3次，适合网络异常恢复
 * - 查询操作：重试5次，适合临时性查询失败
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-21
 */
@ActivityInterface
public interface InventoryReceiptActivity {


    /**
     * 创建快递（独立方法）
     * 
     * 功能说明：
     * - 基于快递查询结果创建快递
     * - 使用快递查询结果中的expressGoodsList作为入参
     * - 支持幂等性，重试时不会重复创建
     * 
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     * 
     * @param request 业务请求
     * @param expressQueryResult 快递查询结果，包含expressGoodsList
     * @return 创建结果，成功时包含快递ID
     */
    @ActivityMethod
    CompanyBusinessResponse createExpressOnly(CompanyBusinessRequest request,
                                              Map<String, Object> resultData);

    /**
     * 基于库存查询结果创建同行单
     *
     * 功能说明：
     * - 使用预先查询好的库存数据创建同行单
     * - 避免重复查询，提高效率
     * - 支持幂等性，重试时不会重复创建
     *
     * 重试策略：
     * - 最大重试次数：3次
     * - 初始间隔：10秒
     * - 退避系数：2.0
     *
     * @param request 业务请求
     * @param stockResultData 库存查询结果数据
     * @return 创建结果，成功时包含同行单ID
     */
    @ActivityMethod
    CompanyBusinessResponse createPeerListWithStockData(CompanyBusinessRequest request,
                                                        Map<String, Object> stockResultData);
}
