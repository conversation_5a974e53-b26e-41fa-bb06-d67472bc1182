package com.vedeng.temporal.workflow.activity.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.enums.SystemSourceEnum;
import com.vedeng.temporal.config.TemporalProperties;
import com.vedeng.temporal.domain.dto.CompletionCondition;
import com.vedeng.temporal.domain.dto.CustomPollingRequest;
import com.vedeng.temporal.domain.dto.PollingRequest;
import com.vedeng.temporal.domain.result.PollingResult;
import com.vedeng.temporal.exception.BusinessProcessException;
import com.vedeng.temporal.exception.ExceptionHandler;
import com.vedeng.temporal.util.SystemApiClient;
import com.vedeng.temporal.validation.BusinessCompletionChecker;
import com.vedeng.temporal.workflow.activity.PollingActivity;
import io.temporal.activity.Activity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 状态轮询活动实现类（重构版）
 * 
 * 重构说明：
 * - 移除 BusinessStatus 依赖，使用 PollingResult<T>
 * - 基于 BusinessCompletionChecker 进行完成判断
 * - 支持携带业务数据的轮询结果
 * - 增加批量轮询和异步轮询功能
 * - 优化性能和错误处理
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (重构版)
 * @since 2025-01-22
 */
@Component
@Slf4j
public class PollingActivityImpl implements PollingActivity {

    @Autowired
    private SystemApiClient systemApiClient;

    @Autowired
    private ExceptionHandler exceptionHandler;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    @Override
    public PollingResult<Map<String, Object>> pollUntilCompleted(PollingRequest request) {
        // 验证请求参数
        request.validate();
        
        TemporalProperties.PollingConfig config = request.getEffectivePollingConfig();
        log.info("开始轮询业务状态，请求: {}, 配置: {}", request.getRequestKey(), config);
        
        if (!config.isEnabled()) {
            log.warn("轮询功能已禁用，返回未完成状态，请求: {}", request.getRequestKey());
            return PollingResult.<Map<String, Object>>failed("轮询功能已禁用")
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
        }
        
        LocalDateTime startTime = LocalDateTime.now();
        Duration maxTimeout = config.getMaxTimeoutDuration();
        Duration currentInterval = config.getInitialIntervalDuration();
        
        int attemptCount = 0;
        int maxRetryCount = config.getMaxRetryCount();
        Map<String, Object> lastData = null;
        boolean maxRetriesReached = false; // 标志位：是否达到最大重试次数
        
        try {
            while (Duration.between(startTime, LocalDateTime.now()).toMillis() < maxTimeout.toMillis()) {
                attemptCount++;
                
                // 检查重试次数限制
                if (maxRetryCount > 0 && attemptCount > maxRetryCount) {
                    log.warn("达到最大重试次数限制，请求: {}, 尝试次数: {}", 
                            request.getRequestKey(), attemptCount);
                    maxRetriesReached = true; // 设置标志位
                    break;
                }
                
                try {
                    // 发送心跳
                    String heartbeatMessage = String.format("轮询中，尝试次数: %d, 间隔: %ds", 
                            attemptCount, currentInterval.getSeconds());
                    Activity.getExecutionContext().heartbeat(heartbeatMessage);
                    
                    if (config.isEnableVerboseLogging()) {
                        log.debug("轮询状态检查，请求: {}, 尝试次数: {}", request.getRequestKey(), attemptCount);
                    }
                    
                    // 执行状态查询
                    PollingResult<Map<String, Object>> checkResult = checkBusinessStatus(request);
                    
                    if (checkResult.isSuccess()) {
                        LocalDateTime endTime = LocalDateTime.now();
                        log.info("轮询完成，请求: {}, 总尝试次数: {}, 总耗时: {}ms", 
                                request.getRequestKey(), attemptCount, 
                                Duration.between(startTime, endTime).toMillis());
                        
                        return checkResult.withExecutionInfo(startTime, endTime, attemptCount)
                                .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
                    }
                    
                    // 保存最后一次查询的数据
                    lastData = checkResult.getData();
                    
                    if (config.isEnableVerboseLogging()) {
                        log.debug("状态未完成，继续轮询，请求: {}", request.getRequestKey());
                    }
                    
                } catch (Exception e) {
                    log.warn("状态查询异常，将重试，请求: {}, 尝试次数: {}, 错误: {}", 
                            request.getRequestKey(), attemptCount, e.getMessage());
                    
                    // 发送异常心跳
                    Activity.getExecutionContext().heartbeat("查询异常: " + e.getMessage());
                }
                
                // 等待下次轮询
                sleepWithHeartbeat(currentInterval, request.getRequestKey());
                
                // 计算下次轮询间隔（指数退避）
                currentInterval = calculateNextInterval(currentInterval, config);
            }
            
            // 检查是否达到最大重试次数
            if (maxRetriesReached) {
                String maxRetriesMessage = String.format("达到最大重试次数限制，请求: %s, 最大重试次数: %d, 实际尝试次数: %d",
                        request.getRequestKey(), maxRetryCount, attemptCount);
                log.error(maxRetriesMessage);
                
                LocalDateTime endTime = LocalDateTime.now();
                return PollingResult.<Map<String, Object>>failed(maxRetriesMessage)
                        .withExecutionInfo(startTime, endTime, attemptCount)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("lastData", lastData);
            }
            
            // 轮询超时
            String timeoutMessage = String.format("轮询超时，请求: %s, 最大等待时间: %d天, 总尝试次数: %d",
                    request.getRequestKey(), config.getMaxTimeoutDays(), attemptCount);
            log.error(timeoutMessage);
            
            LocalDateTime endTime = LocalDateTime.now();
            return PollingResult.<Map<String, Object>>timeout(timeoutMessage, attemptCount)
                    .withExecutionInfo(startTime, endTime, attemptCount)
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                    .addAttribute("lastData", lastData);
            
        } catch (Exception e) {
            log.error("轮询过程异常，请求: {}", request.getRequestKey(), e);
            LocalDateTime endTime = LocalDateTime.now();
            return PollingResult.<Map<String, Object>>failed("轮询失败: " + e.getMessage())
                    .withExecutionInfo(startTime, endTime, attemptCount)
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                    .addAttribute("lastData", lastData);
        }
    }
    
    @Override
    public PollingResult<Map<String, Object>> checkBusinessStatus(PollingRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            log.debug("执行单次状态查询，请求: {}", request.getRequestKey());

            // 获取状态查询API路径
            String apiPath = request.getApiPath();

            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            if (request.getApiParameters() != null && !request.getApiParameters().isEmpty()) {
                requestData.putAll(request.getApiParameters());
                log.debug("使用API参数: {}", request.getApiParameters());
            } else {
                log.warn("API参数为空，请求: {}", request.getRequestKey());
            }

            // 调用状态查询API
            String responseJson = systemApiClient
                    .withCompany(request.getCompanyCode())
                    .postToSystemApi(apiPath, requestData, SystemSourceEnum.TEMPORAL);

            // 解析JSON响应
            Map<String, Object> apiResponse = parseJsonResponse(responseJson);

            if (apiResponse == null) {
                log.warn("状态查询返回空结果，请求: {}", request.getRequestKey());
                return PollingResult.<Map<String, Object>>failed("API响应为空")
                        .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
            }

            // 检查 API 调用是否成功
            Integer code = (Integer) apiResponse.get("code");
            if (code == null || code != 0) {
                log.warn("API调用失败，请求: {}, 响应码: {}", request.getRequestKey(), code);
                return PollingResult.<Map<String, Object>>failed("API调用失败，响应码: " + code)
                        .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("apiResponse", apiResponse);
            }

            // 提取业务数据
            Object dataObj = apiResponse.get("data");
            if (dataObj == null) {
                log.warn("API响应数据为空，请求: {}", request.getRequestKey());
                return PollingResult.<Map<String, Object>>failed("API响应数据为空")
                        .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("apiResponse", apiResponse);
            }

            // 确保业务数据是 Map 类型
            Map<String, Object> businessData;
            if (dataObj instanceof Map) {
                businessData = (Map<String, Object>) dataObj;
            } else {
                log.warn("API响应数据不是Map类型，请求: {}, 数据类型: {}",
                        request.getRequestKey(), dataObj.getClass().getSimpleName());
                return PollingResult.<Map<String, Object>>failed("API响应数据类型错误")
                        .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("apiResponse", apiResponse);
            }

            // 使用业务完成检查器判断状态
            try {
                BusinessCompletionChecker checker = request.getCompletionChecker();
                boolean isCompleted = checker.isCompleted(businessData, request);
                
                LocalDateTime endTime = LocalDateTime.now();
                
                if (isCompleted) {
                    log.debug("业务完成检查通过，请求: {}", request.getRequestKey());
                    return PollingResult.completed(businessData, "业务完成")
                            .withExecutionInfo(startTime, endTime, 1)
                            .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                            .addAttribute("apiResponse", apiResponse);
                } else {
                    log.debug("业务未完成，请求: {}", request.getRequestKey());
                    return PollingResult.<Map<String, Object>>failed("业务未完成")
                            .withExecutionInfo(startTime, endTime, 1)
                            .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                            .addAttribute("businessData", businessData)
                            .addAttribute("apiResponse", apiResponse);
                }
                
            } catch (Exception e) {
                log.warn("完成检查器执行异常，请求: {}, 错误: {}",
                        request.getRequestKey(), e.getMessage());
                return PollingResult.<Map<String, Object>>failed("完成检查器异常: " + e.getMessage())
                        .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("businessData", businessData);
            }

        } catch (Exception e) {
            log.error("状态查询失败，请求: {}", request.getRequestKey(), e);
            return PollingResult.<Map<String, Object>>failed("状态查询失败: " + e.getMessage())
                    .withExecutionInfo(startTime, LocalDateTime.now(), 1)
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
        }
    }

    @Override
    public PollingResult<Map<String, Object>> pollWithCustomQuery(CustomPollingRequest request) {
        // 验证请求参数
        request.validate();
        
        TemporalProperties.PollingConfig config = request.getEffectivePollingConfig();
        log.info("开始自定义轮询业务状态，请求: {}, 查询类型: {}, 配置: {}", 
                request.getRequestKey(), request.getQueryType(), config);
        
        if (!config.isEnabled()) {
            log.warn("轮询功能已禁用，返回未完成状态，请求: {}", request.getRequestKey());
            return PollingResult.<Map<String, Object>>failed("轮询功能已禁用")
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
        }
        
        LocalDateTime startTime = LocalDateTime.now();
        Duration maxTimeout = config.getMaxTimeoutDuration();
        Duration currentInterval = config.getInitialIntervalDuration();
        
        int attemptCount = 0;
        int maxRetryCount = config.getMaxRetryCount();
        Map<String, Object> lastQueryResult = null;
        boolean maxRetriesReached = false; // 标志位：是否达到最大重试次数
        
        try {
            while (Duration.between(startTime, LocalDateTime.now()).toMillis() < maxTimeout.toMillis()) {
                attemptCount++;
                
                // 检查重试次数限制
                if (maxRetryCount > 0 && attemptCount > maxRetryCount) {
                    log.warn("达到最大重试次数限制，请求: {}, 尝试次数: {}", 
                            request.getRequestKey(), attemptCount);
                    maxRetriesReached = true; // 设置标志位
                    break;
                }
                
                try {
                    // 发送心跳
                    String heartbeatMessage = String.format("自定义轮询中，查询类型: %s, 尝试次数: %d, 间隔: %ds", 
                            request.getQueryType(), attemptCount, currentInterval.getSeconds());
                    Activity.getExecutionContext().heartbeat(heartbeatMessage);
                    
                    if (config.isEnableVerboseLogging()) {
                        log.debug("自定义轮询状态检查，请求: {}, 查询类型: {}, 尝试次数: {}", 
                                request.getRequestKey(), request.getQueryType(), attemptCount);
                    }
                    
                    // 根据查询类型执行具体查询
                    Map<String, Object> queryResult = executeCustomQuery(request);
                    lastQueryResult = queryResult;
                    
                    if (queryResult == null) {
                        log.debug("自定义查询返回null，继续轮询，请求: {}", request.getRequestKey());
                    } else {
                        // 使用完成条件列表判断是否完成
                        boolean isCompleted = checkCompletionConditions(queryResult, request.getCompletionConditions());
                        
                        if (isCompleted) {
                            LocalDateTime endTime = LocalDateTime.now();
                            log.info("自定义轮询完成，请求: {}, 查询类型: {}, 总尝试次数: {}, 总耗时: {}ms", 
                                    request.getRequestKey(), request.getQueryType(), attemptCount, 
                                    Duration.between(startTime, endTime).toMillis());
                            
                            return PollingResult.completed(queryResult, "自定义轮询完成")
                                    .withExecutionInfo(startTime, endTime, attemptCount)
                                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode());
                        }
                        
                        if (config.isEnableVerboseLogging()) {
                            log.debug("自定义查询未完成，继续轮询，请求: {}, 查询结果: {}", 
                                    request.getRequestKey(), queryResult);
                        }
                    }
                    
                } catch (Exception e) {
                    log.warn("自定义查询异常，将重试，请求: {}, 尝试次数: {}, 错误: {}", 
                            request.getRequestKey(), attemptCount, e.getMessage());
                    
                    // 发送异常心跳
                    Activity.getExecutionContext().heartbeat("自定义查询异常: " + e.getMessage());
                }
                
                // 等待下次轮询
                sleepWithHeartbeat(currentInterval, request.getRequestKey());
                
                // 计算下次轮询间隔（指数退避）
                currentInterval = calculateNextInterval(currentInterval, config);
            }
            
            // 检查是否达到最大重试次数
            if (maxRetriesReached) {
                String maxRetriesMessage = String.format("自定义轮询达到最大重试次数限制，请求: %s, 查询类型: %s, 最大重试次数: %d, 实际尝试次数: %d",
                        request.getRequestKey(), request.getQueryType(), maxRetryCount, attemptCount);
                log.error(maxRetriesMessage);
                
                LocalDateTime endTime = LocalDateTime.now();
                return PollingResult.<Map<String, Object>>failed(maxRetriesMessage)
                        .withExecutionInfo(startTime, endTime, attemptCount)
                        .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                        .addAttribute("lastQueryResult", lastQueryResult);
            }
            
            // 轮询超时
            String timeoutMessage = String.format("自定义轮询超时，请求: %s, 查询类型: %s, 最大等待时间: %d天, 总尝试次数: %d",
                    request.getRequestKey(), request.getQueryType(), config.getMaxTimeoutDays(), attemptCount);
            log.error(timeoutMessage);
            
            LocalDateTime endTime = LocalDateTime.now();
            return PollingResult.<Map<String, Object>>timeout(timeoutMessage, attemptCount)
                    .withExecutionInfo(startTime, endTime, attemptCount)
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                    .addAttribute("lastQueryResult", lastQueryResult);
            
        } catch (Exception e) {
            log.error("自定义轮询过程异常，请求: {}, 查询类型: {}", request.getRequestKey(), request.getQueryType(), e);
            LocalDateTime endTime = LocalDateTime.now();
            return PollingResult.<Map<String, Object>>failed("自定义轮询失败: " + e.getMessage())
                    .withExecutionInfo(startTime, endTime, attemptCount)
                    .withBusinessContext(request.getBusinessId(), request.getBusinessType(), request.getCompanyCode())
                    .addAttribute("lastQueryResult", lastQueryResult);
        }
    }

    @Override
    public <T> PollingResult<T> pollWithDataType(PollingRequest request, Class<T> dataType) {
        // 先执行标准轮询
        PollingResult<Map<String, Object>> mapResult = pollUntilCompleted(request);
        
        // 转换数据类型
        try {
            T convertedData = convertData(mapResult.getData(), dataType);
            
            return PollingResult.<T>builder()
                    .completed(mapResult.isCompleted())
                    .data(convertedData)
                    .message(mapResult.getMessage())
                    .startTime(mapResult.getStartTime())
                    .endTime(mapResult.getEndTime())
                    .durationMillis(mapResult.getDurationMillis())
                    .attemptCount(mapResult.getAttemptCount())
                    .businessId(mapResult.getBusinessId())
                    .businessType(mapResult.getBusinessType())
                    .companyCode(mapResult.getCompanyCode())
                    .attributes(mapResult.getAttributes())
                    .build();
                    
        } catch (Exception e) {
            log.error("数据类型转换失败，请求: {}, 目标类型: {}", request.getRequestKey(), dataType.getSimpleName(), e);
            return PollingResult.<T>failed("数据类型转换失败: " + e.getMessage())
                    .withExecutionInfo(mapResult.getStartTime(), mapResult.getEndTime(), mapResult.getAttemptCount())
                    .withBusinessContext(mapResult.getBusinessId(), mapResult.getBusinessType(), mapResult.getCompanyCode());
        }
    }

    
    // ========== 私有辅助方法 ==========
    
    /**
     * 根据查询类型执行具体查询（使用枚举方法模式）
     */
    private Map<String, Object> executeCustomQuery(CustomPollingRequest request) {
        return request.getQueryType().executeQuery(request);
    }
    
    /**
     * 检查完成条件
     */
    private boolean checkCompletionConditions(Map<String, Object> queryResult, List<CompletionCondition> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return false;
        }
        
        // 所有条件都必须满足才算完成
        for (CompletionCondition condition : conditions) {
            if (!checkSingleCondition(queryResult, condition)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查单个完成条件
     */
    private boolean checkSingleCondition(Map<String, Object> queryResult, CompletionCondition condition) {
        String fieldName = condition.getFieldName();
        String operator = condition.getOperator();
        Object expectedValue = condition.getExpectedValue();
        
        Object fieldValue = queryResult.get(fieldName);
        
        switch (operator) {
            case CompletionCondition.Operators.NOT_BLANK:
                return fieldValue instanceof String && StrUtil.isNotBlank((String) fieldValue);
                
            case CompletionCondition.Operators.NOT_NULL:
                return fieldValue != null;
                
            case CompletionCondition.Operators.EQUALS:
                return Objects.equals(fieldValue, expectedValue);
                
            case CompletionCondition.Operators.NOT_EQUALS:
                return !Objects.equals(fieldValue, expectedValue);
                
            case CompletionCondition.Operators.GREATER_THAN:
                if (fieldValue instanceof Number && expectedValue instanceof Number) {
                    return ((Number) fieldValue).doubleValue() > ((Number) expectedValue).doubleValue();
                }
                return false;
                
            case CompletionCondition.Operators.LESS_THAN:
                if (fieldValue instanceof Number && expectedValue instanceof Number) {
                    return ((Number) fieldValue).doubleValue() < ((Number) expectedValue).doubleValue();
                }
                return false;
                
            default:
                log.warn("不支持的操作符: {}", operator);
                return false;
        }
    }
    
    /**
     * 在等待期间定期发送心跳的睡眠方法
     */
    private void sleepWithHeartbeat(Duration sleepDuration, String requestKey) {
        long sleepMillis = sleepDuration.toMillis();
        long heartbeatInterval = Math.min(1000, Math.max(500, sleepMillis / 10));
        long sleptTime = 0;

        log.debug("开始等待轮询间隔，总时长: {}ms, 心跳间隔: {}ms, 请求: {}",
                sleepMillis, heartbeatInterval, requestKey);

        while (sleptTime < sleepMillis) {
            long currentSleep = Math.min(heartbeatInterval, sleepMillis - sleptTime);

            try {
                Thread.sleep(currentSleep);
                sleptTime += currentSleep;

                // 每次睡眠后都发送心跳
                String heartbeatMessage = String.format("轮询等待中，已等待: %ds/%ds",
                        sleptTime / 1000, sleepMillis / 1000);
                Activity.getExecutionContext().heartbeat(heartbeatMessage);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("轮询等待被中断，请求: {}", requestKey, e);
                throw new RuntimeException("轮询被中断", e);
            }
        }

        log.debug("轮询间隔等待完成，请求: {}", requestKey);
    }

    /**
     * 计算下次轮询间隔
     */
    private Duration calculateNextInterval(Duration currentInterval, TemporalProperties.PollingConfig config) {
        long nextIntervalSeconds = (long) (currentInterval.getSeconds() * config.getBackoffCoefficient());
        long maxIntervalSeconds = config.getMaxIntervalDuration().getSeconds();

        return Duration.ofSeconds(Math.min(nextIntervalSeconds, maxIntervalSeconds));
    }
    
    /**
     * 解析JSON响应为Map对象
     */
    private Map<String, Object> parseJsonResponse(String responseJson) {
        if (responseJson == null || responseJson.trim().isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(responseJson, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.warn("JSON响应解析失败，原始响应: {}", responseJson, e);
            return null;
        }
    }
    
    /**
     * 数据类型转换
     */
    private <T> T convertData(Map<String, Object> data, Class<T> targetType) throws Exception {
        if (data == null) {
            return null;
        }
        
        if (targetType.isAssignableFrom(data.getClass())) {
            return (T) data;
        }
        
        // 使用 Jackson 进行类型转换
        String json = objectMapper.writeValueAsString(data);
        return objectMapper.readValue(json, targetType);
    }
}